#!/usr/bin/env node

/**
 * Setup script for Revantad Store Admin Dashboard
 * This script helps with initial project setup and environment validation
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan')
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function checkNodeVersion() {
  const nodeVersion = process.version
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
  
  if (majorVersion < 18) {
    logError(`Node.js version ${nodeVersion} is not supported. Please upgrade to Node.js 18 or higher.`)
    process.exit(1)
  }
  
  logSuccess(`Node.js version ${nodeVersion} is supported`)
}

function checkPackageManager() {
  try {
    execSync('npm --version', { stdio: 'ignore' })
    logSuccess('npm is available')
    return 'npm'
  } catch {
    logError('npm is not available. Please install Node.js and npm.')
    process.exit(1)
  }
}

function createEnvFile() {
  const envExamplePath = path.join(process.cwd(), '.env.example')
  const envLocalPath = path.join(process.cwd(), '.env.local')
  
  if (!fs.existsSync(envExamplePath)) {
    logError('.env.example file not found')
    return false
  }
  
  if (fs.existsSync(envLocalPath)) {
    logWarning('.env.local already exists. Skipping creation.')
    return true
  }
  
  try {
    fs.copyFileSync(envExamplePath, envLocalPath)
    logSuccess('.env.local created from .env.example')
    logWarning('Please edit .env.local with your actual environment variables')
    return true
  } catch (error) {
    logError(`Failed to create .env.local: ${error.message}`)
    return false
  }
}

function installDependencies(packageManager) {
  try {
    log('\nInstalling dependencies...', 'blue')
    execSync(`${packageManager} install`, { stdio: 'inherit' })
    logSuccess('Dependencies installed successfully')
    return true
  } catch (error) {
    logError(`Failed to install dependencies: ${error.message}`)
    return false
  }
}

function checkEnvironmentVariables() {
  const envLocalPath = path.join(process.cwd(), '.env.local')
  
  if (!fs.existsSync(envLocalPath)) {
    logWarning('.env.local not found. Some features may not work.')
    return false
  }
  
  const envContent = fs.readFileSync(envLocalPath, 'utf8')
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
  ]
  
  const missingVars = requiredVars.filter(varName => {
    const regex = new RegExp(`^${varName}=.+`, 'm')
    return !regex.test(envContent)
  })
  
  if (missingVars.length > 0) {
    logWarning(`Missing required environment variables: ${missingVars.join(', ')}`)
    logWarning('Please update your .env.local file with the actual values')
    return false
  }
  
  logSuccess('Required environment variables are configured')
  return true
}

function displayNextSteps() {
  log('\n' + '='.repeat(60), 'magenta')
  log('🎉 Setup completed successfully!', 'green')
  log('='.repeat(60), 'magenta')
  
  log('\n📋 Next Steps:', 'cyan')
  log('1. Update .env.local with your actual credentials:', 'yellow')
  log('   - Supabase URL and keys')
  log('   - Cloudinary credentials')
  log('   - NextAuth secret')
  
  log('\n2. Set up your database:', 'yellow')
  log('   - Go to your Supabase dashboard')
  log('   - Run the SQL commands from database/schema.sql')
  
  log('\n3. Start the development server:', 'yellow')
  log('   npm run dev')
  
  log('\n4. Open your browser:', 'yellow')
  log('   http://localhost:3000')
  
  log('\n📚 Documentation:', 'cyan')
  log('   - Setup Guide: docs/SETUP_GUIDE.md')
  log('   - Deployment: docs/DEPLOYMENT.md')
  log('   - README: README.md')
  
  log('\n🆘 Need Help?', 'cyan')
  log('   - Check the documentation')
  log('   - Create an issue on GitHub')
  
  log('\n' + '='.repeat(60), 'magenta')
}

function main() {
  log('🏪 Revantad Store Admin Dashboard Setup', 'bright')
  log('========================================', 'magenta')
  
  logStep(1, 'Checking Node.js version')
  checkNodeVersion()
  
  logStep(2, 'Checking package manager')
  const packageManager = checkPackageManager()
  
  logStep(3, 'Creating environment file')
  createEnvFile()
  
  logStep(4, 'Installing dependencies')
  const depsInstalled = installDependencies(packageManager)
  
  if (!depsInstalled) {
    logError('Setup failed during dependency installation')
    process.exit(1)
  }
  
  logStep(5, 'Checking environment variables')
  checkEnvironmentVariables()
  
  displayNextSteps()
}

// Run the setup
if (require.main === module) {
  main()
}

module.exports = {
  checkNodeVersion,
  checkPackageManager,
  createEnvFile,
  installDependencies,
  checkEnvironmentVariables,
}
