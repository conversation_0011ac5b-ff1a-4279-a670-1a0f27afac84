-- =====================================================
-- CUSTOMER TABLE VERIFICATION SCRIPT
-- =====================================================
-- Run this in Supabase SQL Editor to verify table structure

-- 1. Check if customers table exists and view its structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'customers' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check if specific columns exist
SELECT 
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'phone_number'
    ) THEN 'EXISTS' ELSE 'MISSING' END as phone_number_status,
    
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'address'
    ) THEN 'EXISTS' ELSE 'MISSING' END as address_status,
    
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'birth_date'
    ) THEN 'EXISTS' ELSE 'MISSING' END as birth_date_status,
    
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'birth_place'
    ) THEN 'EXISTS' ELSE 'MISSING' END as birth_place_status;

-- 3. Check sample customer data to see what fields have values
SELECT 
    customer_name,
    customer_family_name,
    phone_number,
    address,
    birth_date,
    birth_place,
    created_at
FROM customers 
ORDER BY created_at DESC 
LIMIT 5;

-- 4. If columns are missing, add them (SAFE - only adds if missing)
DO $$
BEGIN
    -- Add phone_number column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'phone_number'
    ) THEN
        ALTER TABLE customers ADD COLUMN phone_number VARCHAR(20);
        RAISE NOTICE 'Added phone_number column';
    ELSE
        RAISE NOTICE 'phone_number column already exists';
    END IF;

    -- Add address column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'address'
    ) THEN
        ALTER TABLE customers ADD COLUMN address TEXT;
        RAISE NOTICE 'Added address column';
    ELSE
        RAISE NOTICE 'address column already exists';
    END IF;

    -- Add birth_date column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'birth_date'
    ) THEN
        ALTER TABLE customers ADD COLUMN birth_date DATE;
        RAISE NOTICE 'Added birth_date column';
    ELSE
        RAISE NOTICE 'birth_date column already exists';
    END IF;

    -- Add birth_place column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'birth_place'
    ) THEN
        ALTER TABLE customers ADD COLUMN birth_place VARCHAR(255);
        RAISE NOTICE 'Added birth_place column';
    ELSE
        RAISE NOTICE 'birth_place column already exists';
    END IF;
END $$;

-- 5. Verify the fix worked
SELECT 'VERIFICATION AFTER FIX:' as status;

SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'customers' 
AND table_schema = 'public'
AND column_name IN ('phone_number', 'address', 'birth_date', 'birth_place')
ORDER BY column_name;

-- 6. Test insert to verify everything works
INSERT INTO customers (
    customer_name, 
    customer_family_name, 
    phone_number, 
    address, 
    birth_date, 
    birth_place,
    notes
) VALUES (
    'Test', 
    'Customer', 
    '09123456789', 
    'Test Address, Test City', 
    '1990-01-01',
    'Test Birthplace',
    'Test customer for verification - safe to delete'
) ON CONFLICT (customer_name, customer_family_name) DO NOTHING;

-- 7. Verify the test insert
SELECT 
    'TEST INSERT RESULT:' as status,
    customer_name,
    customer_family_name,
    phone_number,
    address,
    birth_date,
    birth_place
FROM customers 
WHERE customer_name = 'Test' AND customer_family_name = 'Customer';

-- 8. Clean up test data (optional)
-- DELETE FROM customers WHERE customer_name = 'Test' AND customer_family_name = 'Customer';
