'use client'

import { X, Package } from 'lucide-react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useState } from 'react'

interface ProductImageZoomProps {
  imageUrl?: string | undefined
  productName: string
  isOpen: boolean
  onClose: () => void
}

export default function ProductImageZoom({
  imageUrl,
  productName,
  isOpen,
  onClose
}: ProductImageZoomProps) {
  const { resolvedTheme } = useTheme()
  const [imageLoaded, setImageLoaded] = useState(false)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[100] p-4">
      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 p-2 rounded-full bg-white/10 text-white hover:bg-white/20 transition-colors z-10"
        title="Close"
      >
        <X className="h-6 w-6" />
      </button>

      {/* Image Container */}
      <div className="relative max-w-4xl max-h-full w-full h-full flex items-center justify-center">
        {imageUrl ? (
          <div className="relative">
            {!imageLoaded && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
              </div>
            )}
            <Image
              src={imageUrl}
              alt={productName}
              width={800}
              height={800}
              className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-opacity duration-300 ${
                imageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={() => setImageLoaded(true)}
              priority
            />
          </div>
        ) : (
          <div 
            className="flex flex-col items-center justify-center p-12 rounded-lg"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
            }}
          >
            <Package className="h-24 w-24 text-gray-400 mb-4" />
            <p className="text-gray-500 text-lg font-medium">No image available</p>
            <p className="text-gray-400 text-sm mt-2">{productName}</p>
          </div>
        )}
      </div>

      {/* Product Name Overlay */}
      <div className="absolute bottom-4 left-4 right-4 text-center">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 inline-block">
          <h3 className="text-white font-semibold text-lg">{productName}</h3>
        </div>
      </div>
    </div>
  )
}

// Hook for using image zoom
export function useImageZoom() {
  const [zoomImage, setZoomImage] = useState<{
    imageUrl?: string | undefined
    productName: string
  } | null>(null)

  const openZoom = (imageUrl: string | undefined, productName: string) => {
    setZoomImage({
      imageUrl: imageUrl,
      productName
    })
  }

  const closeZoom = () => {
    setZoomImage(null)
  }

  return {
    zoomImage,
    openZoom,
    closeZoom,
    isZoomOpen: !!zoomImage
  }
}
