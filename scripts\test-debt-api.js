/**
 * =====================================================
 * DEBT API TESTING SCRIPT
 * =====================================================
 * Professional API testing script for Tindahan Store debt management
 * Use this to test the complete debt creation flow and diagnose issues
 * 
 * 🎯 PURPOSE: Test debt API endpoints and verify database synchronization
 * 📅 CREATED: 2025-07-29
 * 🔧 USAGE: Run with Node.js to test API endpoints
 * =====================================================
 */

// Test configuration
const API_BASE_URL = 'http://localhost:3000/api';
const TEST_DEBT = {
  customer_name: 'Dave',
  customer_family_name: 'Mejos',
  product_name: 'Test Product',
  product_price: 75.00,
  quantity: 1,
  debt_date: new Date().toISOString().split('T')[0],
  notes: 'API Test - Safe to delete'
};

/**
 * Test the debt creation API endpoint
 */
async function testCreateDebt() {
  console.log('🧪 Testing Debt Creation API...');
  console.log('📝 Test Data:', TEST_DEBT);
  
  try {
    const response = await fetch(`${API_BASE_URL}/debts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_DEBT)
    });

    console.log('📡 Response Status:', response.status);
    console.log('📡 Response Headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorData = await response.text();
      console.error('❌ API Error:', errorData);
      return null;
    }

    const result = await response.json();
    console.log('✅ Debt Created Successfully:', result);
    return result.debt;
  } catch (error) {
    console.error('❌ Network Error:', error.message);
    return null;
  }
}

/**
 * Test fetching all debts
 */
async function testFetchDebts() {
  console.log('\n🔍 Testing Fetch Debts API...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/debts`);
    
    if (!response.ok) {
      const errorData = await response.text();
      console.error('❌ Fetch Error:', errorData);
      return null;
    }

    const result = await response.json();
    console.log('✅ Debts Fetched Successfully');
    console.log('📊 Total Records:', result.data?.length || 0);
    
    // Look for Dave Mejos specifically
    const daveDebts = result.data?.filter(debt => 
      debt.customer_name.toLowerCase().includes('dave') || 
      debt.customer_family_name.toLowerCase().includes('mejos')
    ) || [];
    
    console.log('🔍 Dave Mejos Records Found:', daveDebts.length);
    if (daveDebts.length > 0) {
      console.log('📋 Dave Mejos Debts:', daveDebts);
    }
    
    return result.data;
  } catch (error) {
    console.error('❌ Network Error:', error.message);
    return null;
  }
}

/**
 * Test environment variables
 */
function testEnvironment() {
  console.log('\n🔧 Environment Check...');
  
  // Check if we're running in the right environment
  const nodeEnv = process.env.NODE_ENV || 'development';
  console.log('🌍 Node Environment:', nodeEnv);
  
  // Note: In a real test, you'd check Supabase connection here
  // but we can't access .env.local from this script directly
  console.log('⚠️  Note: Run this script from your Next.js application directory');
  console.log('⚠️  Ensure your .env.local file has correct Supabase credentials');
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Debt API Tests...');
  console.log('=' .repeat(60));
  
  // Test environment
  testEnvironment();
  
  // Test fetching existing debts first
  const existingDebts = await testFetchDebts();
  
  // Test creating a new debt
  const newDebt = await testCreateDebt();
  
  // Test fetching debts again to see if the new one appears
  if (newDebt) {
    console.log('\n🔄 Re-fetching debts to verify creation...');
    await testFetchDebts();
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 Test Summary:');
  console.log('- Check the console output above for any errors');
  console.log('- If Dave Mejos appears in fetch but not in Supabase SQL Editor,');
  console.log('  there may be a database connection issue');
  console.log('- Verify your .env.local Supabase credentials');
  console.log('- Check browser Network tab for failed API calls');
  console.log('=' .repeat(60));
}

// Run tests if this script is executed directly
if (typeof window === 'undefined' && require.main === module) {
  // Node.js environment
  const fetch = require('node-fetch');
  runTests().catch(console.error);
} else if (typeof window !== 'undefined') {
  // Browser environment
  window.testDebtAPI = runTests;
  console.log('🌐 Browser environment detected');
  console.log('💡 Run testDebtAPI() in the browser console to start tests');
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testCreateDebt,
    testFetchDebts,
    testEnvironment,
    runTests
  };
}
