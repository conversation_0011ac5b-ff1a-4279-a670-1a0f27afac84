'use client'

import { useTheme } from 'next-themes'
import { useState, useEffect, useCallback } from 'react'

import {
  AdminHeader,
  Sidebar,
  ProductsSection,
  DebtSection,
  DashboardStats,
  FamilyGallery,
  APIGraphing,
  History,
  Calendar,
  Settings,
  ProtectedRoute,
  AIAssistant,
  AISupport,
  ToastContainer,
  useToast
} from '@/components'
import type { DashboardStats as DashboardStatsType } from '@/types'

export default function AdminPage() {
  const [activeSection, setActiveSection] = useState('dashboard')
  const { resolvedTheme } = useTheme()
  const { toasts, removeToast } = useToast()
  const [stats, setStats] = useState<DashboardStatsType>({
    totalProducts: 0,
    lowStockItems: 0,
    recentProducts: []
  })

  // Add debugging for activeSection changes
  useEffect(() => {
    console.log('🎯 AdminPage: activeSection changed to:', activeSection)
  }, [activeSection])

  // Create a safe setActiveSection wrapper with logging
  const safeSetActiveSection = useCallback((newSection: string) => {
    console.log('🎯 AdminPage: setActiveSection called with:', newSection, 'current:', activeSection)
    if (newSection !== activeSection) {
      setActiveSection(newSection)
    }
  }, [activeSection])

  const fetchStats = useCallback(async () => {
    try {
      // Fetch products
      const productsRes = await fetch('/api/products')
      const productsData = await productsRes.json()
      const products = productsData.products || []

      // Calculate stats
      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length

      setStats({
        totalProducts: products.length,
        lowStockItems: lowStockProducts,
        recentProducts: products.slice(0, 5)
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }, [])

  useEffect(() => {
    fetchStats()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  const renderContent = () => {
    console.warn('🎯 Rendering content for section:', activeSection)

    switch (activeSection) {
      case 'products':
        console.warn('📦 Rendering ProductsSection')
        return <ProductsSection onStatsUpdate={fetchStats} />
      case 'debts':
        console.warn('💳 Rendering DebtSection')
        return <DebtSection />
      case 'family-gallery':
        console.warn('🖼️ Rendering FamilyGallery')
        return <FamilyGallery />
      case 'api-graphing':
        console.warn('📊 Rendering APIGraphing')
        return <APIGraphing stats={stats} />
      case 'history':
        console.warn('📜 Rendering History')
        return <History />
      case 'calendar':
        console.warn('📅 Rendering Calendar')
        return <Calendar />
      case 'settings':
        console.warn('⚙️ Rendering Settings')
        return <Settings />
      case 'ai-support':
        console.warn('🤖 Rendering AISupport')
        return <AISupport />
      default:
        console.warn('🏠 Rendering DashboardStats (default)')
        return <DashboardStats stats={stats} onSectionChange={safeSetActiveSection} onRefreshStats={fetchStats} />
    }
  }

  const getPageTitle = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Dashboard'
      case 'products':
        return 'Product Lists'
      case 'debts':
        return 'Debt Management'
      case 'family-gallery':
        return 'Family Gallery'
      case 'api-graphing':
        return 'API Graphing & Visuals'
      case 'history':
        return 'History'
      case 'calendar':
        return 'Calendar'
      case 'settings':
        return 'Settings'
      case 'ai-support':
        return 'AI Support'
      default:
        return 'Dashboard'
    }
  }

  const getPageDescription = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Overview of your Revantad Store'
      case 'products':
        return 'Manage your product lists with CRUD operations'
      case 'debts':
        return 'Track customer debt and payments'
      case 'family-gallery':
        return 'Manage family photos and memories'
      case 'api-graphing':
        return 'Visual analytics and business insights'
      case 'history':
        return 'View transaction and activity history'
      case 'calendar':
        return 'Manage events and schedules'
      case 'settings':
        return 'Configure your store settings'
      case 'ai-support':
        return 'Get intelligent assistance for your store management'
      default:
        return 'Overview of your Revantad Store'
    }
  }

  return (
    <ProtectedRoute>
      <div
        className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'
        }}
      >
        {/* Facebook-style Header */}
        <AdminHeader
          activeSection={activeSection}
          setActiveSection={safeSetActiveSection}
        />

        <div className="flex pt-16">
          {/* Updated Sidebar */}
          <Sidebar activeSection={activeSection} setActiveSection={safeSetActiveSection} />

          {/* Main Content */}
          <main
            className="flex-1 transition-colors duration-300 main-content-scroll"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',
              height: 'calc(100vh - 4rem)',
              overflowY: 'auto',
              overflowX: 'hidden'
            }}
          >
            <div className="p-8">
              <div className="mb-8">
                <h1
                  className="text-3xl font-bold transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'
                  }}
                >
                  {getPageTitle()}
                </h1>
                <p
                  className="mt-2 transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  {getPageDescription()}
                </p>
              </div>
              {renderContent()}
            </div>
          </main>
        </div>

        {/* AI Assistant */}
        <AIAssistant context={activeSection} />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
      </div>
    </ProtectedRoute>
  )
}
