/**
 * =====================================================
 * DAVE MEJOS SYNCHRONIZATION FIX SCRIPT
 * =====================================================
 * Professional script to fix the Dave Mejos database synchronization issue
 * This script will clear frontend cache and verify database state
 * 
 * 🎯 PURPOSE: Fix <PERSON> appearing in localhost but not in database
 * 📅 CREATED: 2025-07-29
 * 🔧 USAGE: Run in browser console or Node.js
 * =====================================================
 */

const API_BASE_URL = 'http://localhost:3001/api';

/**
 * Clear all browser cache and localStorage
 */
function clearBrowserCache() {
    console.log('🧹 Clearing browser cache and localStorage...');
    
    // Clear localStorage
    if (typeof localStorage !== 'undefined') {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.includes('revantad') || key.includes('debt') || key.includes('customer')) {
                localStorage.removeItem(key);
                console.log(`   ✅ Removed localStorage key: ${key}`);
            }
        });
    }
    
    // Clear sessionStorage
    if (typeof sessionStorage !== 'undefined') {
        const keys = Object.keys(sessionStorage);
        keys.forEach(key => {
            if (key.includes('revantad') || key.includes('debt') || key.includes('customer')) {
                sessionStorage.removeItem(key);
                console.log(`   ✅ Removed sessionStorage key: ${key}`);
            }
        });
    }
    
    console.log('✅ Browser cache cleared successfully');
}

/**
 * Verify Dave Mejos in database
 */
async function verifyDaveMejosInDatabase() {
    console.log('🔍 Verifying Dave Mejos in database...');
    
    try {
        // Check debts
        const debtsResponse = await fetch(`${API_BASE_URL}/debts`);
        const debtsData = await debtsResponse.json();
        const debts = debtsData.data?.debts || [];
        
        const daveDebts = debts.filter(debt => 
            debt.customer_name.toLowerCase().includes('dave') || 
            debt.customer_family_name.toLowerCase().includes('mejos')
        );
        
        console.log(`📊 Total debts in database: ${debts.length}`);
        console.log(`🔍 Dave Mejos debts found: ${daveDebts.length}`);
        
        if (daveDebts.length > 0) {
            console.log('✅ Dave Mejos found in debts:', daveDebts);
        } else {
            console.log('❌ Dave Mejos NOT found in debts table');
        }
        
        // Check balances
        const balancesResponse = await fetch(`${API_BASE_URL}/customer-balances`);
        const balancesData = await balancesResponse.json();
        const balances = balancesData.data?.balances || [];
        
        const daveBalance = balances.filter(balance => 
            balance.customer_name.toLowerCase().includes('dave') || 
            balance.customer_family_name.toLowerCase().includes('mejos')
        );
        
        console.log(`📊 Total balances in database: ${balances.length}`);
        console.log(`🔍 Dave Mejos balance found: ${daveBalance.length}`);
        
        if (daveBalance.length > 0) {
            console.log('✅ Dave Mejos found in balances:', daveBalance);
        } else {
            console.log('❌ Dave Mejos NOT found in customer_balances view');
        }
        
        return {
            debtsCount: daveDebts.length,
            balancesCount: daveBalance.length,
            daveDebts,
            daveBalance
        };
        
    } catch (error) {
        console.error('❌ Error verifying database:', error);
        return null;
    }
}

/**
 * Create Dave Mejos debt record for testing
 */
async function createDaveMejosDebt() {
    console.log('➕ Creating Dave Mejos debt record...');
    
    const testDebt = {
        customer_name: 'Dave',
        customer_family_name: 'Mejos',
        product_name: 'Test Product - Database Sync Fix',
        product_price: 100.00,
        quantity: 1,
        debt_date: new Date().toISOString().split('T')[0],
        notes: 'Created by sync fix script - Safe to delete'
    };
    
    try {
        const response = await fetch(`${API_BASE_URL}/debts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testDebt)
        });
        
        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`API Error: ${response.status} ${errorData}`);
        }
        
        const result = await response.json();
        console.log('✅ Dave Mejos debt created successfully:', result);
        return result.debt;
        
    } catch (error) {
        console.error('❌ Error creating Dave Mejos debt:', error);
        return null;
    }
}

/**
 * Force refresh frontend data
 */
function forceRefreshFrontend() {
    console.log('🔄 Forcing frontend refresh...');
    
    // Trigger a hard refresh
    if (typeof window !== 'undefined') {
        // Clear any React state or cache
        if (window.location) {
            console.log('   🔄 Performing hard refresh...');
            window.location.reload(true);
        }
    } else {
        console.log('   ⚠️  Not in browser environment - manual refresh required');
    }
}

/**
 * Main fix function
 */
async function fixDaveMejosIssue() {
    console.log('🚀 Starting Dave Mejos synchronization fix...');
    console.log('=' .repeat(60));
    
    // Step 1: Clear cache
    clearBrowserCache();
    
    // Step 2: Verify current state
    const verification = await verifyDaveMejosInDatabase();
    
    if (!verification) {
        console.log('❌ Failed to verify database state');
        return;
    }
    
    // Step 3: If Dave Mejos is missing, create a test record
    if (verification.debtsCount === 0) {
        console.log('🔧 Dave Mejos missing from database - creating test record...');
        const newDebt = await createDaveMejosDebt();
        
        if (newDebt) {
            // Verify again
            console.log('🔍 Re-verifying after creation...');
            await verifyDaveMejosInDatabase();
        }
    } else {
        console.log('✅ Dave Mejos already exists in database');
    }
    
    // Step 4: Instructions for user
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 NEXT STEPS FOR USER:');
    console.log('1. Clear your browser cache (Ctrl+Shift+Delete)');
    console.log('2. Hard refresh the page (Ctrl+F5)');
    console.log('3. Check if Dave Mejos now appears correctly');
    console.log('4. If still not working, check Supabase SQL Editor');
    console.log('5. Run the verification queries in the unified schema');
    console.log('=' .repeat(60));
    
    return verification;
}

// Export for use in different environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        clearBrowserCache,
        verifyDaveMejosInDatabase,
        createDaveMejosDebt,
        forceRefreshFrontend,
        fixDaveMejosIssue
    };
}

// Auto-run in browser console
if (typeof window !== 'undefined') {
    window.fixDaveMejosIssue = fixDaveMejosIssue;
    console.log('🌐 Browser environment detected');
    console.log('💡 Run fixDaveMejosIssue() in the console to start the fix');
}

// Auto-run in Node.js
if (typeof window === 'undefined' && require.main === module) {
    const fetch = require('node-fetch');
    fixDaveMejosIssue().catch(console.error);
}
