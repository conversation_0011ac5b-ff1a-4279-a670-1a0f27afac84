'use client'

import { useTheme } from 'next-themes'

interface LoadingSkeletonProps {
  type: 'products' | 'debts' | 'card' | 'list'
  count?: number
}

export default function LoadingSkeleton({ type, count = 6 }: LoadingSkeletonProps) {
  const { resolvedTheme } = useTheme()

  const skeletonBaseStyle = {
    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
    backgroundImage: resolvedTheme === 'dark' 
      ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
      : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
    backgroundSize: '200% 100%',
    animation: 'shimmer 2s infinite'
  }

  const ProductSkeleton = () => (
    <div 
      className="rounded-lg shadow-md overflow-hidden"
      style={{
        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
        border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
      }}
    >
      {/* Image skeleton */}
      <div 
        className="aspect-square"
        style={skeletonBaseStyle}
      />
      
      {/* Content skeleton */}
      <div className="p-4 space-y-3">
        {/* Title */}
        <div 
          className="h-5 rounded"
          style={skeletonBaseStyle}
        />
        
        {/* Category */}
        <div 
          className="h-4 w-2/3 rounded"
          style={skeletonBaseStyle}
        />
        
        {/* Price and weight */}
        <div className="flex justify-between items-center">
          <div 
            className="h-6 w-20 rounded"
            style={skeletonBaseStyle}
          />
          <div 
            className="h-4 w-16 rounded"
            style={skeletonBaseStyle}
          />
        </div>
        
        {/* Stock */}
        <div 
          className="h-4 w-24 rounded"
          style={skeletonBaseStyle}
        />
        
        {/* Buttons */}
        <div className="flex space-x-2">
          <div 
            className="flex-1 h-10 rounded"
            style={skeletonBaseStyle}
          />
          <div 
            className="flex-1 h-10 rounded"
            style={skeletonBaseStyle}
          />
        </div>
      </div>
    </div>
  )

  const DebtSkeleton = () => (
    <div 
      className="rounded-lg shadow-md overflow-hidden"
      style={{
        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
        border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
      }}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div className="space-y-2">
            <div 
              className="h-6 w-48 rounded"
              style={skeletonBaseStyle}
            />
            <div 
              className="h-4 w-32 rounded"
              style={skeletonBaseStyle}
            />
          </div>
          <div 
            className="h-8 w-24 rounded"
            style={skeletonBaseStyle}
          />
        </div>
      </div>
      
      {/* Content */}
      <div className="divide-y divide-gray-200">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="px-6 py-4">
            <div className="flex justify-between items-start">
              <div className="flex-1 space-y-2">
                <div 
                  className="h-5 w-40 rounded"
                  style={skeletonBaseStyle}
                />
                <div className="space-y-1">
                  <div 
                    className="h-4 w-56 rounded"
                    style={skeletonBaseStyle}
                  />
                  <div 
                    className="h-4 w-48 rounded"
                    style={skeletonBaseStyle}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2 ml-4">
                <div 
                  className="h-6 w-20 rounded"
                  style={skeletonBaseStyle}
                />
                <div 
                  className="h-8 w-8 rounded"
                  style={skeletonBaseStyle}
                />
                <div 
                  className="h-8 w-8 rounded"
                  style={skeletonBaseStyle}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  if (type === 'products') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {[...Array(count)].map((_, i) => (
          <ProductSkeleton key={i} />
        ))}
      </div>
    )
  }

  if (type === 'debts') {
    return (
      <div className="space-y-6">
        {[...Array(count)].map((_, i) => (
          <DebtSkeleton key={i} />
        ))}
      </div>
    )
  }

  return null
}
