#!/usr/bin/env node

/**
 * Test Script for Sukli/Change Management Functionality
 * 
 * This script tests the sukli confirmation and clearing functionality
 * to ensure that sukli is properly hidden after confirmation.
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testSuklifunctionality() {
  console.log('🧪 Testing Sukli/Change Management Functionality...\n')

  try {
    // Step 1: Create a test customer with overpayment
    console.log('📝 Step 1: Creating test customer with overpayment...')
    
    const testCustomer = {
      customer_name: 'Test',
      customer_family_name: 'Suk<PERSON>',
      product_name: 'Test Product',
      product_price: 50.00,
      quantity: 1,
      debt_date: new Date().toISOString().split('T')[0],
      notes: 'Test debt for sukli functionality'
    }

    // Create debt
    const { data: debt, error: debtError } = await supabase
      .from('customer_debts')
      .insert([testCustomer])
      .select()
      .single()

    if (debtError) {
      console.error('❌ Error creating test debt:', debtError.message)
      return
    }

    console.log('✅ Test debt created:', debt.id)

    // Create overpayment
    const testPayment = {
      customer_name: 'Test',
      customer_family_name: 'Sukli',
      payment_amount: 75.00, // ₱25 overpayment
      payment_date: new Date().toISOString().split('T')[0],
      payment_method: 'Cash',
      notes: 'Test payment creating overpayment'
    }

    const { data: payment, error: paymentError } = await supabase
      .from('customer_payments')
      .insert([testPayment])
      .select()
      .single()

    if (paymentError) {
      console.error('❌ Error creating test payment:', paymentError.message)
      return
    }

    console.log('✅ Test payment created:', payment.id)

    // Step 2: Check customer balance before sukli confirmation
    console.log('\n📊 Step 2: Checking customer balance before sukli confirmation...')
    
    const response1 = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/customer-balances`)
    const balanceData1 = await response1.json()
    
    const testCustomerBalance1 = balanceData1.balances?.find(b => 
      b.customer_name === 'Test' && b.customer_family_name === 'Sukli'
    )

    if (testCustomerBalance1) {
      console.log('📈 Balance before sukli confirmation:')
      console.log(`   Total Debt: ₱${testCustomerBalance1.total_debt}`)
      console.log(`   Total Payments: ₱${testCustomerBalance1.total_payments}`)
      console.log(`   Remaining Balance: ₱${testCustomerBalance1.remaining_balance}`)
      console.log(`   Change Amount: ₱${testCustomerBalance1.change_amount}`)
      console.log(`   Status: ${testCustomerBalance1.balance_status}`)
    }

    // Step 3: Confirm sukli given
    console.log('\n✅ Step 3: Confirming sukli given...')
    
    const sukliPayment = {
      customer_name: 'Test',
      customer_family_name: 'Sukli',
      payment_amount: 0.01, // Minimal amount
      payment_method: 'Others',
      notes: `SUKLI_GIVEN:25.00 - Sukli of ₱25.00 given to customer`,
      payment_date: new Date().toISOString().split('T')[0]
    }

    const sukliResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/payments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sukliPayment)
    })

    if (!sukliResponse.ok) {
      console.error('❌ Error recording sukli:', await sukliResponse.text())
      return
    }

    console.log('✅ Sukli confirmation recorded')

    // Step 4: Check customer balance after sukli confirmation
    console.log('\n📊 Step 4: Checking customer balance after sukli confirmation...')
    
    const response2 = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/customer-balances`)
    const balanceData2 = await response2.json()
    
    const testCustomerBalance2 = balanceData2.balances?.find(b => 
      b.customer_name === 'Test' && b.customer_family_name === 'Sukli'
    )

    if (testCustomerBalance2) {
      console.log('📈 Balance after sukli confirmation:')
      console.log(`   Total Debt: ₱${testCustomerBalance2.total_debt}`)
      console.log(`   Total Payments: ₱${testCustomerBalance2.total_payments}`)
      console.log(`   Remaining Balance: ₱${testCustomerBalance2.remaining_balance}`)
      console.log(`   Change Amount: ₱${testCustomerBalance2.change_amount}`)
      console.log(`   Status: ${testCustomerBalance2.balance_status}`)
    }

    // Step 5: Verify the fix
    console.log('\n🔍 Step 5: Verifying sukli functionality...')
    
    const isFixed = testCustomerBalance2?.change_amount === 0 && testCustomerBalance2?.balance_status === 'Paid'
    
    if (isFixed) {
      console.log('🎉 SUCCESS: Sukli functionality is working correctly!')
      console.log('   ✅ Change amount is now 0')
      console.log('   ✅ Balance status is "Paid"')
    } else {
      console.log('❌ FAILED: Sukli functionality is not working correctly')
      console.log('   Expected: change_amount = 0, balance_status = "Paid"')
      console.log(`   Actual: change_amount = ${testCustomerBalance2?.change_amount}, balance_status = "${testCustomerBalance2?.balance_status}"`)
    }

    // Step 6: Cleanup test data
    console.log('\n🧹 Step 6: Cleaning up test data...')
    
    // Delete test payments
    await supabase
      .from('customer_payments')
      .delete()
      .eq('customer_name', 'Test')
      .eq('customer_family_name', 'Sukli')

    // Delete test debt
    await supabase
      .from('customer_debts')
      .delete()
      .eq('id', debt.id)

    console.log('✅ Test data cleaned up')

    console.log('\n🎯 Test completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message)
  }
}

// Run the test
testSuklifunctionality()
