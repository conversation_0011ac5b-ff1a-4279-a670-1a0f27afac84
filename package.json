{"name": "revantad-store-admin", "version": "1.0.0", "private": true, "description": "Professional admin dashboard for Revantad Store - Modern sari-sari store management system", "keywords": ["sari-sari store", "admin dashboard", "product management", "customer debt", "business analytics", "Philippines"], "author": "Revantad Store Team", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "clean": "powershell -Command \"Remove-Item -Recurse -Force .next, node_modules/.cache -ErrorAction SilentlyContinue\"", "db:setup": "echo 'Run the SQL commands from database/schema.sql in your Supabase dashboard'", "preview": "npm run build && npm run start", "setup": "node scripts/setup.js", "setup:env": "node scripts/env-setup-helper.js", "validate": "node scripts/validate-setup.js"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.3", "cloudinary": "^2.7.0", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "zod": "^3.25.75"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}