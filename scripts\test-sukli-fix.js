#!/usr/bin/env node

/**
 * Professional Test Script for Sukli (Change) Functionality Fix
 * 
 * This script tests the sukli functionality to ensure that when a customer
 * has overpaid and sukli is given, the change amount is properly removed
 * from their balance.
 * 
 * Test Scenario:
 * 1. Create a test customer with overpayment
 * 2. Verify the change amount is calculated correctly
 * 3. Simulate giving sukli (record debt adjustment)
 * 4. Verify the change amount is cleared
 * 5. Clean up test data
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Please check your .env.local file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

// Test data
const TEST_CUSTOMER = {
  customer_name: 'Test',
  customer_family_name: 'SukliFix',
  product_name: 'Test Product',
  product_price: 100.00,
  quantity: 1,
  payment_amount: 150.00 // Overpayment of 50.00
}

async function runSukliTest() {
  console.log('🧪 Starting Sukli Functionality Test')
  console.log('=====================================')

  try {
    // Step 1: Clean up any existing test data
    console.log('🧹 Cleaning up existing test data...')
    await cleanupTestData()

    // Step 2: Create test debt
    console.log('📝 Creating test debt record...')
    const { data: debt, error: debtError } = await supabase
      .from('customer_debts')
      .insert([{
        customer_name: TEST_CUSTOMER.customer_name,
        customer_family_name: TEST_CUSTOMER.customer_family_name,
        product_name: TEST_CUSTOMER.product_name,
        product_price: TEST_CUSTOMER.product_price,
        quantity: TEST_CUSTOMER.quantity,
        debt_date: new Date().toISOString().split('T')[0],
        notes: 'Test debt for sukli functionality'
      }])
      .select()
      .single()

    if (debtError) {
      throw new Error(`Failed to create test debt: ${debtError.message}`)
    }

    console.log('✅ Test debt created:', debt)

    // Step 3: Create overpayment
    console.log('💰 Creating overpayment...')
    const { data: payment, error: paymentError } = await supabase
      .from('customer_payments')
      .insert([{
        customer_name: TEST_CUSTOMER.customer_name,
        customer_family_name: TEST_CUSTOMER.customer_family_name,
        payment_amount: TEST_CUSTOMER.payment_amount,
        payment_date: new Date().toISOString().split('T')[0],
        payment_method: 'Cash',
        notes: 'Test overpayment for sukli functionality'
      }])
      .select()
      .single()

    if (paymentError) {
      throw new Error(`Failed to create test payment: ${paymentError.message}`)
    }

    console.log('✅ Test overpayment created:', payment)

    // Step 4: Check initial balance (should show overpayment)
    console.log('📊 Checking initial customer balance...')
    const { data: initialBalance, error: balanceError } = await supabase
      .from('customer_balances')
      .select('*')
      .eq('customer_name', TEST_CUSTOMER.customer_name)
      .eq('customer_family_name', TEST_CUSTOMER.customer_family_name)
      .single()

    if (balanceError) {
      throw new Error(`Failed to fetch initial balance: ${balanceError.message}`)
    }

    console.log('📈 Initial Balance:', {
      total_debt: initialBalance.total_debt,
      total_payments: initialBalance.total_payments,
      remaining_balance: initialBalance.remaining_balance,
      change_amount: initialBalance.change_amount,
      balance_status: initialBalance.balance_status
    })

    // Verify overpayment
    const expectedChange = TEST_CUSTOMER.payment_amount - TEST_CUSTOMER.product_price
    if (Math.abs(initialBalance.change_amount - expectedChange) > 0.01) {
      throw new Error(`Expected change amount ${expectedChange}, got ${initialBalance.change_amount}`)
    }

    console.log('✅ Overpayment correctly calculated!')

    // Step 5: Simulate sukli given (create debt adjustment)
    console.log('🎯 Simulating sukli given...')
    const { data: sukliAdjustment, error: sukliError } = await supabase
      .from('customer_debts')
      .insert([{
        customer_name: TEST_CUSTOMER.customer_name,
        customer_family_name: TEST_CUSTOMER.customer_family_name,
        product_name: 'SUKLI_ADJUSTMENT',
        product_price: initialBalance.change_amount,
        quantity: 1,
        debt_date: new Date().toISOString().split('T')[0],
        notes: `Sukli adjustment - ₱${initialBalance.change_amount.toFixed(2)} change given to customer`
      }])
      .select()
      .single()

    if (sukliError) {
      throw new Error(`Failed to create sukli adjustment: ${sukliError.message}`)
    }

    console.log('✅ Sukli adjustment created:', sukliAdjustment)

    // Step 6: Check final balance (should show no overpayment)
    console.log('📊 Checking final customer balance...')
    const { data: finalBalance, error: finalBalanceError } = await supabase
      .from('customer_balances')
      .select('*')
      .eq('customer_name', TEST_CUSTOMER.customer_name)
      .eq('customer_family_name', TEST_CUSTOMER.customer_family_name)
      .single()

    if (finalBalanceError) {
      throw new Error(`Failed to fetch final balance: ${finalBalanceError.message}`)
    }

    console.log('📈 Final Balance:', {
      total_debt: finalBalance.total_debt,
      total_payments: finalBalance.total_payments,
      remaining_balance: finalBalance.remaining_balance,
      change_amount: finalBalance.change_amount,
      balance_status: finalBalance.balance_status
    })

    // Verify sukli was properly handled
    if (finalBalance.change_amount > 0.01) {
      throw new Error(`Expected change amount to be 0, got ${finalBalance.change_amount}`)
    }

    if (finalBalance.remaining_balance > 0.01) {
      throw new Error(`Expected remaining balance to be 0, got ${finalBalance.remaining_balance}`)
    }

    console.log('✅ Sukli functionality working correctly!')

    // Step 7: Clean up test data
    console.log('🧹 Cleaning up test data...')
    await cleanupTestData()

    console.log('')
    console.log('🎉 SUCCESS: Sukli functionality test passed!')
    console.log('✅ Overpayments are correctly calculated')
    console.log('✅ Sukli adjustments properly balance accounts')
    console.log('✅ Change amounts are cleared after sukli is given')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    
    // Clean up on failure
    console.log('🧹 Cleaning up test data after failure...')
    await cleanupTestData()
    
    process.exit(1)
  }
}

async function cleanupTestData() {
  // Delete test payments
  await supabase
    .from('customer_payments')
    .delete()
    .eq('customer_name', TEST_CUSTOMER.customer_name)
    .eq('customer_family_name', TEST_CUSTOMER.customer_family_name)

  // Delete test debts
  await supabase
    .from('customer_debts')
    .delete()
    .eq('customer_name', TEST_CUSTOMER.customer_name)
    .eq('customer_family_name', TEST_CUSTOMER.customer_family_name)

  console.log('✅ Test data cleaned up')
}

// Run the test
runSukliTest()
