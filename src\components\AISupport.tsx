'use client'

import { motion } from 'framer-motion'
import {
  <PERSON><PERSON>,
  User,
  Send,
  Loader2,
  TrendingUp,
  Package,
  CreditCard,
  BarChart3,
  Lightbulb,
  HelpCircle,
  Zap,
  Brain
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useRef, useEffect } from 'react'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}

const quickPrompts = [
  {
    icon: TrendingUp,
    title: 'Sales Analysis',
    prompt: 'Analyze my current sales performance and suggest improvements'
  },
  {
    icon: Package,
    title: 'Inventory Management',
    prompt: 'Help me optimize my inventory levels and identify slow-moving products'
  },
  {
    icon: CreditCard,
    title: 'Debt Management',
    prompt: 'Provide strategies for managing customer debts effectively'
  },
  {
    icon: BarChart3,
    title: 'Business Insights',
    prompt: 'Give me insights on how to grow my sari-sari store business'
  },
  {
    icon: Lightbulb,
    title: 'Marketing Ideas',
    prompt: 'Suggest marketing strategies to attract more customers'
  },
  {
    icon: HelpCircle,
    title: 'General Help',
    prompt: 'What can you help me with regarding my store management?'
  }
]

export default function AISupport() {
  const { resolvedTheme } = useTheme()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Welcome to AI Support! I\'m here to help you manage your Revantad Store more effectively. I can assist with business analytics, inventory management, customer relationships, financial planning, and much more. How can I help you today?',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const sendMessage = async (messageContent?: string) => {
    const content = messageContent || inputMessage.trim()
    if (!content || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          context: 'ai-support'
        })
      })

      const data = await response.json()

      if (data.success) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: data.response,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, aiMessage])
      } else {
        throw new Error(data.error || 'Failed to get AI response')
      }
    } catch {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <motion.div
            className="p-3 rounded-xl mr-3"
            style={{
              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
              boxShadow: '0 4px 8px rgba(139, 92, 246, 0.3)'
            }}
            whileHover={{ scale: 1.05 }}
          >
            <Brain className="w-6 h-6 text-white" />
          </motion.div>
          <div>
            <h2
              className="text-xl font-bold"
              style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
            >
              AI Business Assistant
            </h2>
            <p
              className="text-sm opacity-80"
              style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#64748b' }}
            >
              Intelligent support for your sari-sari store operations
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-6">
        <h3
          className="text-lg font-semibold mb-4 flex items-center"
          style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
        >
          <Zap className="w-5 h-5 mr-2 text-purple-500" />
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickPrompts.map((prompt, index) => {
            const Icon = prompt.icon
            return (
              <motion.button
                key={index}
                onClick={() => sendMessage(prompt.prompt)}
                className="p-5 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 text-left"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',
                  borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',
                  boxShadow: resolvedTheme === 'dark'
                    ? '0 4px 6px rgba(0, 0, 0, 0.1)'
                    : '0 4px 6px rgba(0, 0, 0, 0.05)'
                }}
                whileHover={{ 
                  scale: 1.03,
                  boxShadow: resolvedTheme === 'dark'
                    ? '0 8px 25px rgba(139, 92, 246, 0.2)'
                    : '0 8px 25px rgba(139, 92, 246, 0.15)'
                }}
                whileTap={{ scale: 0.98 }}
                disabled={isLoading}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex items-center mb-3">
                  <motion.div
                    className="p-2 rounded-lg mr-3"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.2)' : 'rgba(139, 92, 246, 0.1)',
                      border: `1px solid ${resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)'}`
                    }}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    <Icon className="w-5 h-5 text-purple-500" />
                  </motion.div>
                  <span
                    className="font-semibold text-sm"
                    style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                  >
                    {prompt.title}
                  </span>
                </div>
                <p
                  className="text-xs leading-relaxed"
                  style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#64748b' }}
                >
                  {prompt.prompt}
                </p>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 flex flex-col">
        <div
          className="flex-1 rounded-xl border p-4 mb-4 overflow-hidden"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(255, 255, 255, 0.8)',
            borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 4px 6px rgba(0, 0, 0, 0.1)'
              : '0 4px 6px rgba(0, 0, 0, 0.05)'
          }}
        >
          <div className="h-96 overflow-y-auto space-y-4 mb-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex items-start space-x-3 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <div
                    className="p-2 rounded-full flex-shrink-0"
                    style={{
                      backgroundColor: message.type === 'user' 
                        ? (resolvedTheme === 'dark' ? '#22c55e' : '#16a34a')
                        : (resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed')
                    }}
                  >
                    {message.type === 'user' ? (
                      <User className="w-4 h-4 text-white" />
                    ) : (
                      <Bot className="w-4 h-4 text-white" />
                    )}
                  </div>
                  <div
                    className={`p-4 rounded-2xl ${message.type === 'user' ? 'rounded-tr-md' : 'rounded-tl-md'}`}
                    style={{
                      backgroundColor: message.type === 'user'
                        ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')
                        : (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.4)' : 'rgba(243, 244, 246, 0.9)'),
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                    <p
                      className="text-xs mt-2 opacity-70"
                      style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                    >
                      {formatTime(message.timestamp)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-start space-x-3">
                  <div
                    className="p-2 rounded-full"
                    style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                  >
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div
                    className="p-4 rounded-2xl rounded-tl-md flex items-center space-x-3"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.4)' : 'rgba(243, 244, 246, 0.9)'
                    }}
                  >
                    <Loader2 className="w-4 h-4 animate-spin" style={{ color: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }} />
                    <span className="text-sm" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
                      Analyzing your request...
                    </span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="flex items-center space-x-3">
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your store management..."
              disabled={isLoading}
              className="flex-1 p-4 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            />
            <button
              onClick={() => sendMessage()}
              disabled={!inputMessage.trim() || isLoading}
              className="p-4 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500"
              style={{
                background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                boxShadow: '0 4px 8px rgba(139, 92, 246, 0.3)'
              }}
            >
              <Send className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
