-- =====================================================
-- CUSTOMER PROFILE LINKING DIAGNOSTIC SCRIPT
-- =====================================================
-- Run this in Supabase SQL Editor to diagnose the customer profile issue

-- 1. Check what customers exist in the customers table
SELECT 'EXISTING CUSTOMERS IN CUSTOMERS TABLE:' as info;
SELECT 
    customer_name,
    customer_family_name,
    phone_number,
    address,
    birth_date,
    birth_place,
    created_at
FROM customers 
ORDER BY created_at DESC;

-- 2. Check what customers exist in debt records
SELECT 'CUSTOMERS WITH DEBT RECORDS:' as info;
SELECT DISTINCT
    customer_name,
    customer_family_name,
    COUNT(*) as debt_count,
    SUM(total_amount) as total_debt,
    MIN(debt_date) as first_debt_date,
    MAX(debt_date) as last_debt_date
FROM customer_debts 
GROUP BY customer_name, customer_family_name
ORDER BY last_debt_date DESC;

-- 3. Check for customers with debts but no profile
SELECT 'CUSTOMERS WITH DEBTS BUT NO PROFILE:' as info;
SELECT DISTINCT
    cd.customer_name,
    cd.customer_family_name,
    COUNT(cd.id) as debt_count,
    SUM(cd.total_amount) as total_debt
FROM customer_debts cd
LEFT JOIN customers c ON (
    c.customer_name = cd.customer_name 
    AND c.customer_family_name = cd.customer_family_name
)
WHERE c.id IS NULL
GROUP BY cd.customer_name, cd.customer_family_name
ORDER BY total_debt DESC;

-- 4. Check for customers with profiles but no debts
SELECT 'CUSTOMERS WITH PROFILES BUT NO DEBTS:' as info;
SELECT 
    c.customer_name,
    c.customer_family_name,
    c.created_at
FROM customers c
LEFT JOIN customer_debts cd ON (
    cd.customer_name = c.customer_name 
    AND cd.customer_family_name = c.customer_family_name
)
WHERE cd.id IS NULL
ORDER BY c.created_at DESC;

-- 5. Check specific customer (Dave Mejos from the screenshot)
SELECT 'DAVE MEJOS SPECIFIC CHECK:' as info;

-- Check if Dave Mejos exists in customers table
SELECT 'Dave Mejos in customers table:' as check_type, COUNT(*) as count
FROM customers 
WHERE customer_name = 'Dave' AND customer_family_name = 'Mejos'
UNION ALL
-- Check if Dave Mejos has debt records
SELECT 'Dave Mejos debt records:' as check_type, COUNT(*) as count
FROM customer_debts 
WHERE customer_name = 'Dave' AND customer_family_name = 'Mejos';

-- 6. Show Dave Mejos debt details if they exist
SELECT 'DAVE MEJOS DEBT DETAILS:' as info;
SELECT 
    product_name,
    product_price,
    quantity,
    total_amount,
    debt_date,
    notes,
    created_at
FROM customer_debts 
WHERE customer_name = 'Dave' AND customer_family_name = 'Mejos'
ORDER BY debt_date DESC;

-- 7. Create missing customer profiles for customers with debts
SELECT 'CREATING MISSING CUSTOMER PROFILES:' as info;

INSERT INTO customers (customer_name, customer_family_name, notes)
SELECT DISTINCT
    cd.customer_name,
    cd.customer_family_name,
    'Auto-created profile for existing debt customer'
FROM customer_debts cd
LEFT JOIN customers c ON (
    c.customer_name = cd.customer_name 
    AND c.customer_family_name = cd.customer_family_name
)
WHERE c.id IS NULL
ON CONFLICT (customer_name, customer_family_name) DO NOTHING;

-- 8. Verify the fix - check if all debt customers now have profiles
SELECT 'VERIFICATION AFTER AUTO-CREATION:' as info;
SELECT 
    'Customers with debts but no profile:' as check_type,
    COUNT(*) as count
FROM (
    SELECT DISTINCT cd.customer_name, cd.customer_family_name
    FROM customer_debts cd
    LEFT JOIN customers c ON (
        c.customer_name = cd.customer_name 
        AND c.customer_family_name = cd.customer_family_name
    )
    WHERE c.id IS NULL
) missing_profiles;

-- 9. Final verification - show all customers with their debt status
SELECT 'FINAL CUSTOMER STATUS:' as info;
SELECT 
    c.customer_name,
    c.customer_family_name,
    c.phone_number,
    c.address,
    c.birth_date,
    c.birth_place,
    COALESCE(debt_summary.debt_count, 0) as debt_count,
    COALESCE(debt_summary.total_debt, 0) as total_debt,
    CASE 
        WHEN debt_summary.debt_count > 0 THEN 'Has Debts'
        ELSE 'No Debts'
    END as debt_status,
    c.created_at as profile_created
FROM customers c
LEFT JOIN (
    SELECT 
        customer_name,
        customer_family_name,
        COUNT(*) as debt_count,
        SUM(total_amount) as total_debt
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debt_summary ON (
    debt_summary.customer_name = c.customer_name 
    AND debt_summary.customer_family_name = c.customer_family_name
)
ORDER BY debt_summary.total_debt DESC NULLS LAST, c.created_at DESC;

SELECT '✅ DIAGNOSTIC COMPLETE!' as status;
SELECT 'All customers with debt records should now have customer profiles.' as message;
