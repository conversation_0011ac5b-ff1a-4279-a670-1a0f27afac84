-- =====================================================
-- MIGRATION: Add image_public_id to products table
-- =====================================================
-- This migration adds Cloudinary public_id support for automatic image cleanup
-- when product images are updated or deleted.
--
-- 🎯 FEATURES ADDED:
-- ✅ image_public_id column for Cloudinary cleanup
-- ✅ Backward compatibility with existing products
-- ✅ Safe migration with rollback support
--
-- 📅 Created: 2025-01-08
-- 🔧 Version: 1.0.0
-- =====================================================

-- Add image_public_id column to products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS image_public_id TEXT;

-- Add comment for documentation
COMMENT ON COLUMN products.image_public_id IS 'Cloudinary public ID for automatic image cleanup when updating or deleting products';

-- Create index for faster lookups (optional, for performance)
CREATE INDEX IF NOT EXISTS idx_products_image_public_id ON products(image_public_id) WHERE image_public_id IS NOT NULL;

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================
-- To rollback this migration, run:
-- ALTER TABLE products DROP COLUMN IF EXISTS image_public_id;
-- DROP INDEX IF EXISTS idx_products_image_public_id;
-- =====================================================

-- Verify the migration
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
AND column_name = 'image_public_id';

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Migration completed successfully: Added image_public_id column to products table';
    RAISE NOTICE '🔧 Cloudinary automatic cleanup is now enabled for product images';
END $$;
