'use client'

import { X, Save, AlertCircle, User, Package, Calendar, DollarSign, Hash, FileText } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect, useMemo } from 'react'

import { VALIDATION_RULES } from '@/constants'
import { CustomerDebt } from '@/lib/supabase'
import { roundToCurrency, parseCurrencyInput } from '@/utils'

interface DebtModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  debt?: CustomerDebt | null
  customerName?: string
  customerFamilyName?: string
}

export default function DebtModal({
  isOpen,
  onClose,
  onSave,
  debt,
  customerName = '',
  customerFamilyName = ''
}: DebtModalProps) {
  const { resolvedTheme } = useTheme()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_family_name: '',
    product_name: '',
    product_price: '',
    quantity: '1',
    debt_date: new Date().toISOString().split('T')[0],
    notes: ''
  })

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Initialize form data when debt prop changes or customer info is provided
  useEffect(() => {
    if (debt) {
      setFormData({
        customer_name: debt.customer_name,
        customer_family_name: debt.customer_family_name,
        product_name: debt.product_name,
        product_price: roundToCurrency(Number(debt.product_price)).toFixed(2),
        quantity: debt.quantity.toString(),
        debt_date: debt.debt_date,
        notes: debt.notes || ''
      })
    } else {
      setFormData({
        customer_name: customerName,
        customer_family_name: customerFamilyName,
        product_name: '',
        product_price: '',
        quantity: '1',
        debt_date: new Date().toISOString().split('T')[0],
        notes: ''
      })
    }
    setErrors({})
    setError(null)
  }, [debt, isOpen, customerName, customerFamilyName])

  // Validation function
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Customer name validation
    if (!formData.customer_name.trim()) {
      newErrors.customer_name = 'Customer name is required'
    } else if (formData.customer_name.length < VALIDATION_RULES.debt.customerName.minLength) {
      newErrors.customer_name = `Customer name must be at least ${VALIDATION_RULES.debt.customerName.minLength} characters`
    } else if (formData.customer_name.length > VALIDATION_RULES.debt.customerName.maxLength) {
      newErrors.customer_name = `Customer name must not exceed ${VALIDATION_RULES.debt.customerName.maxLength} characters`
    }

    // Family name validation
    if (!formData.customer_family_name.trim()) {
      newErrors.customer_family_name = 'Family name is required'
    } else if (formData.customer_family_name.length < VALIDATION_RULES.debt.familyName.minLength) {
      newErrors.customer_family_name = `Family name must be at least ${VALIDATION_RULES.debt.familyName.minLength} characters`
    } else if (formData.customer_family_name.length > VALIDATION_RULES.debt.familyName.maxLength) {
      newErrors.customer_family_name = `Family name must not exceed ${VALIDATION_RULES.debt.familyName.maxLength} characters`
    }

    // Product name validation
    if (!formData.product_name.trim()) {
      newErrors.product_name = 'Product name is required'
    } else if (formData.product_name.length < VALIDATION_RULES.debt.productName.minLength) {
      newErrors.product_name = `Product name must be at least ${VALIDATION_RULES.debt.productName.minLength} characters`
    } else if (formData.product_name.length > VALIDATION_RULES.debt.productName.maxLength) {
      newErrors.product_name = `Product name must not exceed ${VALIDATION_RULES.debt.productName.maxLength} characters`
    }

    // Product price validation
    const price = parseFloat(formData.product_price)
    if (!formData.product_price.trim()) {
      newErrors.product_price = 'Product price is required'
    } else if (isNaN(price)) {
      newErrors.product_price = 'Product price must be a valid number'
    } else if (price < VALIDATION_RULES.debt.productPrice.min) {
      newErrors.product_price = `Product price must be at least ₱${VALIDATION_RULES.debt.productPrice.min}`
    } else if (price > VALIDATION_RULES.debt.productPrice.max) {
      newErrors.product_price = `Product price must not exceed ₱${VALIDATION_RULES.debt.productPrice.max.toLocaleString()}`
    }

    // Quantity validation
    const quantity = parseInt(formData.quantity)
    if (!formData.quantity.trim()) {
      newErrors.quantity = 'Quantity is required'
    } else if (isNaN(quantity)) {
      newErrors.quantity = 'Quantity must be a valid number'
    } else if (quantity < VALIDATION_RULES.debt.quantity.min) {
      newErrors.quantity = `Quantity must be at least ${VALIDATION_RULES.debt.quantity.min}`
    } else if (quantity > VALIDATION_RULES.debt.quantity.max) {
      newErrors.quantity = `Quantity must not exceed ${VALIDATION_RULES.debt.quantity.max.toLocaleString()}`
    }

    // Debt date validation
    if (!formData.debt_date) {
      newErrors.debt_date = 'Debt date is required'
    } else {
      const debtDate = new Date(formData.debt_date)
      const today = new Date()
      today.setHours(23, 59, 59, 999) // End of today
      
      if (debtDate > today) {
        newErrors.debt_date = 'Debt date cannot be in the future'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      const url = debt ? `/api/debts/${debt.id}` : '/api/debts'
      const method = debt ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customer_name: formData.customer_name.trim(),
          customer_family_name: formData.customer_family_name.trim(),
          product_name: formData.product_name.trim(),
          product_price: parseCurrencyInput(formData.product_price),
          quantity: parseInt(formData.quantity),
          debt_date: formData.debt_date,
          notes: formData.notes.trim() || null
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save debt record')
      }

      onSave()
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save debt record')
    } finally {
      setLoading(false)
    }
  }

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    // Special handling for product_price to prevent precision issues
    if (field === 'product_price' && value) {
      // Ensure the value has at most 2 decimal places
      const decimalMatch = value.match(/^\d*\.?\d{0,2}$/)
      if (!decimalMatch) {
        return // Don't update if it would create more than 2 decimal places
      }
    }

    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Calculate total amount
  const totalAmount = useMemo(() => {
    const price = parseCurrencyInput(formData.product_price)
    const quantity = parseInt(formData.quantity) || 0
    const total = price * quantity
    return roundToCurrency(total)
  }, [formData.product_price, formData.quantity])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div
        className="w-full max-w-2xl max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b" style={{
          borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
        }}>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {debt ? 'Edit Debt Record' : 'Add New Debt Record'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Message */}
          {error && (
            <div className="flex items-center p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
              <p className="text-red-700 dark:text-red-300">{error}</p>
            </div>
          )}

          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <User className="h-5 w-5 mr-2" />
              Customer Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="debt-customer-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Customer Name *
                </label>
                <input
                  id="debt-customer-name"
                  name="customer_name"
                  type="text"
                  value={formData.customer_name}
                  onChange={(e) => handleInputChange('customer_name', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                    errors.customer_name
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                  }`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  placeholder="Enter customer name"
                  autoComplete="given-name"
                />
                {errors.customer_name && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.customer_name}</p>
                )}
              </div>

              <div>
                <label htmlFor="debt-customer-family-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Family Name *
                </label>
                <input
                  id="debt-customer-family-name"
                  name="customer_family_name"
                  type="text"
                  value={formData.customer_family_name}
                  onChange={(e) => handleInputChange('customer_family_name', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                    errors.customer_family_name
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                  }`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  placeholder="Enter family name"
                  autoComplete="family-name"
                />
                {errors.customer_family_name && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.customer_family_name}</p>
                )}
              </div>
            </div>
          </div>

          {/* Product Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Product Information
            </h3>

            <div>
              <label htmlFor="debt-product-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Product Name *
              </label>
              <input
                id="debt-product-name"
                name="product_name"
                type="text"
                value={formData.product_name}
                onChange={(e) => handleInputChange('product_name', e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                  errors.product_name
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                }`}
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                placeholder="Enter product name"
                autoComplete="off"
              />
              {errors.product_name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.product_name}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="debt-product-price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Product Price (₱) *
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    id="debt-product-price"
                    name="product_price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.product_price}
                    onChange={(e) => handleInputChange('product_price', e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                      errors.product_price
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                    placeholder="0.00"
                    autoComplete="off"
                  />
                </div>
                {errors.product_price && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.product_price}</p>
                )}
              </div>

              <div>
                <label htmlFor="debt-quantity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Quantity *
                </label>
                <div className="relative">
                  <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    id="debt-quantity"
                    name="quantity"
                    type="number"
                    min="1"
                    value={formData.quantity}
                    onChange={(e) => handleInputChange('quantity', e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                      errors.quantity
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                    placeholder="1"
                    autoComplete="off"
                  />
                </div>
                {errors.quantity && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.quantity}</p>
                )}
              </div>
            </div>

            {/* Total Amount Display */}
            {totalAmount > 0 && (
              <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-green-700 dark:text-green-300">Total Amount:</span>
                  <span className="text-lg font-bold text-green-800 dark:text-green-200">
                    ₱{totalAmount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Additional Information
            </h3>

            <div>
              <label htmlFor="debt-date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Debt Date *
              </label>
              <input
                id="debt-date"
                name="debt_date"
                type="date"
                value={formData.debt_date}
                onChange={(e) => handleInputChange('debt_date', e.target.value)}
                max={new Date().toISOString().split('T')[0]}
                className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                  errors.debt_date
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                }`}
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                autoComplete="off"
              />
              {errors.debt_date && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.debt_date}</p>
              )}
            </div>

            <div>
              <label htmlFor="debt-notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes (Optional)
              </label>
              <div className="relative">
                <FileText className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <textarea
                  id="debt-notes"
                  name="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500 transition-colors resize-none"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  placeholder="Add any additional notes about this debt..."
                  autoComplete="off"
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 pt-6 border-t" style={{
            borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {debt ? 'Updating...' : 'Saving...'}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {debt ? 'Update Debt' : 'Save Debt'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
