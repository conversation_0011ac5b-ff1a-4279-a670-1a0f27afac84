'use client'

import { ChevronLeft, ChevronRight, Plus, Clock, MapPin, Users, Calendar as CalendarIcon, Moon } from 'lucide-react'
import { useState } from 'react'

// Moon Phase Types and Interfaces
interface MoonPhase {
  name: string
  emoji: string
  icon: string
  illumination: number
  description: string
}

interface MoonPhaseData {
  phase: MoonPhase
  age: number
  illumination: number
  nextFullMoon: Date
  nextNewMoon: Date
}

interface Event {
  id: string
  title: string
  description: string
  date: string
  time: string
  type: 'delivery' | 'meeting' | 'reminder' | 'holiday' | 'personal'
  location?: string
  attendees?: string[]
}

// Bisaya-Tagalog Language Dictionary
const BISAYA_TAGALOG_TEXTS = {
  // Calendar Terms
  calendar: 'Kalendaryo',
  today: 'Karon nga Adlaw',
  events: 'Mga Panghitabo',
  schedule: 'Iskedyul',

  // Moon Phase Terms
  moonPhases: 'Mga Hugis sa Bulan',
  moonPhase: 'Hugis sa Bulan',
  newMoon: 'Bag-ong Bulan',
  waxingCrescent: 'Nagdako nga Sungay',
  firstQuarter: 'Una nga Bahin',
  waxingGibbous: 'Nagdako nga Bula',
  fullMoon: 'Puno nga Bulan',
  waningGibbous: 'Nagliit nga Bula',
  lastQuarter: 'Katapusan nga Bahin',
  waningCrescent: 'Nagliit nga Sungay',

  // Moon Phase Descriptions
  newMoonDesc: 'Ang bulan dili makita gikan sa yuta',
  waxingCrescentDesc: 'Nipis nga sungay sa bulan sa tuo nga bahin',
  firstQuarterDesc: 'Katunga sa bulan nag-hayag sa tuo nga bahin',
  waxingGibbousDesc: 'Sobra sa katunga sa bulan nag-hayag',
  fullMoonDesc: 'Tibuok nga bulan nag-hayag ug makita',
  waningGibbousDesc: 'Sobra sa katunga nag-hayag, nagliit na',
  lastQuarterDesc: 'Katunga sa bulan nag-hayag sa wala nga bahin',
  waningCrescentDesc: 'Nipis nga sungay sa bulan sa wala nga bahin',

  // UI Elements
  addEvent: 'Dugang Event',
  manage: 'Pagdumala',
  upcoming: 'Umaabot na',
  legend: 'Giya',
  age: 'Edad',
  illumination: 'Kahayag',
  days: 'mga adlaw',
  next: 'Sunod',
  cancel: 'Kanselar',
  description: 'Deskripsyon',
  location: 'Lugar',
  time: 'Oras',
  date: 'Petsa',
  title: 'Titulo',
  type: 'Klase',
  attendees: 'Mga Apil',
  more: 'pa',

  // Event Types
  delivery: 'Delivery',
  meeting: 'Meeting',
  reminder: 'Pahinumdom',
  holiday: 'Holiday',
  personal: 'Personal'
}

// Moon Phase Calculation Functions
const getMoonPhases = (): MoonPhase[] => [
  {
    name: BISAYA_TAGALOG_TEXTS.newMoon,
    emoji: '🌑',
    icon: 'new-moon',
    illumination: 0,
    description: BISAYA_TAGALOG_TEXTS.newMoonDesc
  },
  {
    name: BISAYA_TAGALOG_TEXTS.waxingCrescent,
    emoji: '🌒',
    icon: 'waxing-crescent',
    illumination: 25,
    description: BISAYA_TAGALOG_TEXTS.waxingCrescentDesc
  },
  {
    name: BISAYA_TAGALOG_TEXTS.firstQuarter,
    emoji: '🌓',
    icon: 'first-quarter',
    illumination: 50,
    description: BISAYA_TAGALOG_TEXTS.firstQuarterDesc
  },
  {
    name: BISAYA_TAGALOG_TEXTS.waxingGibbous,
    emoji: '🌔',
    icon: 'waxing-gibbous',
    illumination: 75,
    description: BISAYA_TAGALOG_TEXTS.waxingGibbousDesc
  },
  {
    name: BISAYA_TAGALOG_TEXTS.fullMoon,
    emoji: '🌕',
    icon: 'full-moon',
    illumination: 100,
    description: BISAYA_TAGALOG_TEXTS.fullMoonDesc
  },
  {
    name: BISAYA_TAGALOG_TEXTS.waningGibbous,
    emoji: '🌖',
    icon: 'waning-gibbous',
    illumination: 75,
    description: BISAYA_TAGALOG_TEXTS.waningGibbousDesc
  },
  {
    name: BISAYA_TAGALOG_TEXTS.lastQuarter,
    emoji: '🌗',
    icon: 'last-quarter',
    illumination: 50,
    description: BISAYA_TAGALOG_TEXTS.lastQuarterDesc
  },
  {
    name: BISAYA_TAGALOG_TEXTS.waningCrescent,
    emoji: '🌘',
    icon: 'waning-crescent',
    illumination: 25,
    description: BISAYA_TAGALOG_TEXTS.waningCrescentDesc
  }
]

const calculateMoonPhase = (date: Date): MoonPhaseData => {
  // Known new moon date: January 6, 2000, 18:14 UTC
  const knownNewMoon = new Date(2000, 0, 6, 18, 14)
  const lunarCycle = 29.53058867 // Average lunar cycle in days

  // Calculate days since known new moon
  const daysSinceNewMoon = (date.getTime() - knownNewMoon.getTime()) / (1000 * 60 * 60 * 24)

  // Calculate current position in lunar cycle
  const cyclePosition = daysSinceNewMoon % lunarCycle
  const moonAge = cyclePosition < 0 ? cyclePosition + lunarCycle : cyclePosition

  // Calculate illumination percentage
  const illumination = Math.round((1 - Math.cos((moonAge / lunarCycle) * 2 * Math.PI)) * 50)

  // Determine moon phase based on age
  const phases = getMoonPhases()
  let phaseIndex = 0

  if (moonAge < 1.84566) phaseIndex = 0      // New Moon
  else if (moonAge < 5.53699) phaseIndex = 1 // Waxing Crescent
  else if (moonAge < 9.22831) phaseIndex = 2 // First Quarter
  else if (moonAge < 12.91963) phaseIndex = 3 // Waxing Gibbous
  else if (moonAge < 16.61096) phaseIndex = 4 // Full Moon
  else if (moonAge < 20.30228) phaseIndex = 5 // Waning Gibbous
  else if (moonAge < 23.99361) phaseIndex = 6 // Last Quarter
  else phaseIndex = 7                        // Waning Crescent

  // Ensure phaseIndex is within bounds
  phaseIndex = Math.max(0, Math.min(phaseIndex, phases.length - 1))

  // Calculate next full moon and new moon dates
  const daysToNextFullMoon = (14.76529 - moonAge + lunarCycle) % lunarCycle
  const daysToNextNewMoon = (lunarCycle - moonAge) % lunarCycle

  const nextFullMoon = new Date(date.getTime() + daysToNextFullMoon * 24 * 60 * 60 * 1000)
  const nextNewMoon = new Date(date.getTime() + daysToNextNewMoon * 24 * 60 * 60 * 1000)

  const selectedPhase = phases[phaseIndex]
  if (!selectedPhase) {
    // Fallback to new moon if phase is undefined
    const fallbackPhase = phases[0]
    if (!fallbackPhase) {
      throw new Error('No moon phases available')
    }
    return {
      phase: fallbackPhase,
      age: Math.round(moonAge * 10) / 10,
      illumination,
      nextFullMoon,
      nextNewMoon
    }
  }

  return {
    phase: selectedPhase,
    age: Math.round(moonAge * 10) / 10,
    illumination,
    nextFullMoon,
    nextNewMoon
  }
}

// Moon Phase Icon Component
const MoonPhaseIcon = ({ phase, size = 16, className = "" }: { phase: MoonPhase, size?: number, className?: string }) => {
  const iconStyle = {
    width: size,
    height: size,
    fontSize: size,
    display: 'inline-block',
    lineHeight: 1
  }

  return (
    <span
      className={`moon-phase-icon ${className}`}
      style={iconStyle}
      title={`${phase.name} - ${phase.description}`}
    >
      {phase.emoji}
    </span>
  )
}

// Moon Phase Tooltip Component
const MoonPhaseTooltip = ({ moonData, className = "" }: { moonData: MoonPhaseData, className?: string }) => {
  return (
    <div className={`absolute z-10 p-3 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 min-w-48 ${className}`}>
      <div className="flex items-center space-x-2 mb-2">
        <MoonPhaseIcon phase={moonData.phase} size={20} />
        <span className="font-semibold text-gray-900 dark:text-white">{moonData.phase.name}</span>
      </div>
      <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
        <p>{moonData.phase.description}</p>
        <p>{BISAYA_TAGALOG_TEXTS.age}: {moonData.age} {BISAYA_TAGALOG_TEXTS.days}</p>
        <p>{BISAYA_TAGALOG_TEXTS.illumination}: {moonData.illumination}%</p>
        <p className="text-xs pt-1 border-t border-gray-200 dark:border-gray-600">
          {BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.fullMoon}: {moonData.nextFullMoon.toLocaleDateString('tl-PH')}
        </p>
      </div>
    </div>
  )
}

export default function Calendar() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [isEventModalOpen, setIsEventModalOpen] = useState(false)
  const [showMoonPhases, setShowMoonPhases] = useState(true)
  const [hoveredMoonPhase, setHoveredMoonPhase] = useState<{ date: Date, moonData: MoonPhaseData, position: { x: number, y: number } } | null>(null)
  const [events, setEvents] = useState<Event[]>([
    {
      id: '1',
      title: 'Supplier Delivery',
      description: 'Weekly grocery delivery from main supplier',
      date: '2024-01-22',
      time: '09:00',
      type: 'delivery',
      location: 'Store Front',
    },
    {
      id: '2',
      title: 'Monthly Inventory Check',
      description: 'Complete inventory count and stock verification',
      date: '2024-01-25',
      time: '14:00',
      type: 'reminder',
    },
    {
      id: '3',
      title: 'Community Meeting',
      description: 'Barangay business owners meeting',
      date: '2024-01-28',
      time: '16:00',
      type: 'meeting',
      location: 'Barangay Hall',
      attendees: ['Maria Santos', 'Juan Dela Cruz', 'Ana Reyes'],
    },
    {
      id: '4',
      title: 'New Year Holiday',
      description: 'Store closed for New Year celebration',
      date: '2024-01-01',
      time: '00:00',
      type: 'holiday',
    },
  ])

  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    date: '',
    time: '',
    type: 'reminder' as Event['type'],
    location: '',
  })

  const monthNames = [
    'Enero', 'Pebrero', 'Marso', 'Abril', 'Mayo', 'Hunyo',
    'Hulyo', 'Agosto', 'Septyembre', 'Oktubre', 'Nobyembre', 'Disyembre'
  ]

  const daysOfWeek = ['Dom', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab']

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }
    
    return days
  }

  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    return events.filter(event => event.date === dateString)
  }

  const getEventTypeColor = (type: Event['type']) => {
    switch (type) {
      case 'delivery':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
      case 'meeting':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
      case 'reminder':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
      case 'holiday':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
      case 'personal':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const handleAddEvent = (e: React.FormEvent) => {
    e.preventDefault()
    if (newEvent.title && newEvent.date && newEvent.time) {
      const event: Event = {
        id: Date.now().toString(),
        ...newEvent,
      }
      setEvents([...events, event])
      setNewEvent({
        title: '',
        description: '',
        date: '',
        time: '',
        type: 'reminder',
        location: '',
      })
      setIsEventModalOpen(false)
    }
  }

  const days = getDaysInMonth(currentDate)
  const today = new Date()

  return (
    <div className="space-y-6 bisaya-calendar">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white cultural-accent bisaya-text">{BISAYA_TAGALOG_TEXTS.calendar}</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1 bisaya-text">
            {BISAYA_TAGALOG_TEXTS.manage} sa inyong store {BISAYA_TAGALOG_TEXTS.events} ug {BISAYA_TAGALOG_TEXTS.schedule} uban sa lunar phases
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowMoonPhases(!showMoonPhases)}
            className={`flex items-center px-3 py-2 rounded-lg border transition-all duration-200 hover:scale-105 ${
              showMoonPhases
                ? 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900/30 dark:border-blue-600 dark:text-blue-400 shadow-md'
                : 'border-gray-300 text-gray-600 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-slate-700'
            }`}
          >
            <Moon className="h-4 w-4 mr-2" />
            {BISAYA_TAGALOG_TEXTS.moonPhases}
          </button>
          <button
            onClick={() => setIsEventModalOpen(true)}
            className="btn-primary flex items-center hover:scale-105 shadow-lg"
          >
            <Plus className="h-4 w-4 mr-2" />
            {BISAYA_TAGALOG_TEXTS.addEvent}
          </button>
        </div>
      </div>

      {/* Calendar Navigation */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={() => navigateMonth('prev')}
              className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => setCurrentDate(new Date())}
              className="px-4 py-2 text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105 shadow-sm"
            >
              {BISAYA_TAGALOG_TEXTS.today}
            </button>
            <button
              onClick={() => navigateMonth('next')}
              className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Day headers */}
          {daysOfWeek.map(day => (
            <div key={day} className="p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
              {day}
            </div>
          ))}
          
          {/* Calendar days */}
          {days.map((day, index) => {
            if (!day) {
              return <div key={index} className="p-3 h-28"></div>
            }

            const dayEvents = getEventsForDate(day)
            const isToday = day.toDateString() === today.toDateString()
            const isSelected = selectedDate?.toDateString() === day.toDateString()
            const moonData = calculateMoonPhase(day)

            return (
              <div
                key={index}
                onClick={() => setSelectedDate(day)}
                className={`calendar-day-cell p-2 h-28 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 relative ${
                  isToday ? 'bg-green-50 dark:bg-green-900/20 ring-1 ring-green-300 dark:ring-green-600' : ''
                } ${isSelected ? 'ring-2 ring-green-500 bg-green-100 dark:bg-green-900/30' : ''}`}
              >
                {/* Day number and moon phase */}
                <div className="flex items-center justify-between mb-1">
                  <div className={`text-sm font-medium ${
                    isToday ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'
                  }`}>
                    {day.getDate()}
                  </div>
                  {showMoonPhases && (
                    <div
                      className="moon-phase-container relative"
                      onMouseEnter={(e) => {
                        const rect = e.currentTarget.getBoundingClientRect()
                        setHoveredMoonPhase({
                          date: day,
                          moonData,
                          position: { x: rect.left, y: rect.top }
                        })
                      }}
                      onMouseLeave={() => setHoveredMoonPhase(null)}
                    >
                      <MoonPhaseIcon
                        phase={moonData.phase}
                        size={14}
                        className="opacity-80 hover:opacity-100 transition-opacity duration-200"
                      />
                    </div>
                  )}
                </div>

                {/* Events */}
                <div className="space-y-1">
                  {dayEvents.slice(0, showMoonPhases ? 1 : 2).map(event => (
                    <div
                      key={event.id}
                      className={`text-xs px-1 py-0.5 rounded truncate ${getEventTypeColor(event.type)}`}
                    >
                      {event.title}
                    </div>
                  ))}
                  {dayEvents.length > (showMoonPhases ? 1 : 2) && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      +{dayEvents.length - (showMoonPhases ? 1 : 2)} {BISAYA_TAGALOG_TEXTS.more}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Moon Phase Information Panel */}
      {showMoonPhases && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Current Moon Phase */}
          <div className="card p-4 animate-fade-in-up filipino-shadow transition-all duration-300">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text">
              <Moon className="h-5 w-5 mr-2 text-blue-500" />
              {BISAYA_TAGALOG_TEXTS.moonPhase} Karon nga Adlaw
            </h3>
            {(() => {
              const todayMoon = calculateMoonPhase(today)
              return (
                <div className="text-center">
                  <div className="mb-3">
                    <MoonPhaseIcon phase={todayMoon.phase} size={48} className="mx-auto" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{todayMoon.phase.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{todayMoon.phase.description}</p>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200">
                      <div className="font-medium text-gray-900 dark:text-white">{BISAYA_TAGALOG_TEXTS.age}</div>
                      <div className="text-gray-600 dark:text-gray-400">{todayMoon.age} {BISAYA_TAGALOG_TEXTS.days}</div>
                    </div>
                    <div className="bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200">
                      <div className="font-medium text-gray-900 dark:text-white">{BISAYA_TAGALOG_TEXTS.illumination}</div>
                      <div className="text-gray-600 dark:text-gray-400">{todayMoon.illumination}%</div>
                    </div>
                  </div>
                </div>
              )
            })()}
          </div>

          {/* Upcoming Moon Events */}
          <div className="card p-4 animate-fade-in-up filipino-shadow transition-all duration-300">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text">
              <span className="text-lg mr-2">🌙</span>
              {BISAYA_TAGALOG_TEXTS.upcoming} nga Bulan {BISAYA_TAGALOG_TEXTS.events}
            </h3>
            {(() => {
              const todayMoon = calculateMoonPhase(today)
              return (
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200">
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">🌕</span>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.fullMoon}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {todayMoon.nextFullMoon.toLocaleDateString('tl-PH', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200">
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">🌑</span>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.newMoon}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {todayMoon.nextNewMoon.toLocaleDateString('tl-PH', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })()}
          </div>

          {/* Moon Phase Legend */}
          <div className="card p-4 animate-fade-in-up filipino-shadow transition-all duration-300">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text">
              <span className="text-lg mr-2">📖</span>
              {BISAYA_TAGALOG_TEXTS.legend} sa {BISAYA_TAGALOG_TEXTS.moonPhases}
            </h3>
            <div className="grid grid-cols-1 gap-2">
              {getMoonPhases().map((phase, index) => (
                <div key={index} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 hover:scale-102">
                  <MoonPhaseIcon phase={phase} size={18} />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{phase.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{phase.illumination}% {BISAYA_TAGALOG_TEXTS.illumination}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Moon Phase Tooltip */}
      {hoveredMoonPhase && (
        <div
          className="fixed pointer-events-none z-50"
          style={{
            left: hoveredMoonPhase.position.x,
            top: hoveredMoonPhase.position.y - 10,
            transform: 'translateY(-100%)'
          }}
        >
          <MoonPhaseTooltip moonData={hoveredMoonPhase.moonData} />
        </div>
      )}

      {/* Upcoming Events */}
      <div className="card p-6 hover:shadow-lg transition-all duration-300">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <span className="text-lg mr-2">📅</span>
          {BISAYA_TAGALOG_TEXTS.upcoming} nga {BISAYA_TAGALOG_TEXTS.events}
        </h3>
        
        <div className="space-y-3">
          {events
            .filter(event => new Date(event.date) >= today)
            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
            .slice(0, 5)
            .map(event => (
              <div key={event.id} className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className={`p-2 rounded-lg ${getEventTypeColor(event.type)}`}>
                  <CalendarIcon className="h-4 w-4" />
                </div>
                
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-white">{event.title}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{event.description}</p>
                  
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>{new Date(event.date).toLocaleDateString()} at {event.time}</span>
                    </div>
                    
                    {event.location && (
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-3 w-3" />
                        <span>{event.location}</span>
                      </div>
                    )}
                    
                    {event.attendees && (
                      <div className="flex items-center space-x-1">
                        <Users className="h-3 w-3" />
                        <span>{event.attendees.length} {BISAYA_TAGALOG_TEXTS.attendees}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Add Event Modal */}
      {isEventModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md shadow-2xl animate-fade-in-up">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Plus className="h-5 w-5 mr-2 text-green-500" />
              Dugang Bag-ong {BISAYA_TAGALOG_TEXTS.events}
            </h3>
            
            <form onSubmit={handleAddEvent} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {BISAYA_TAGALOG_TEXTS.title} sa {BISAYA_TAGALOG_TEXTS.events}
                </label>
                <input
                  type="text"
                  value={newEvent.title}
                  onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200"
                  placeholder="I-type ang titulo sa event..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {BISAYA_TAGALOG_TEXTS.description}
                </label>
                <textarea
                  value={newEvent.description}
                  onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200"
                  placeholder="Detalye sa event..."
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {BISAYA_TAGALOG_TEXTS.date}
                  </label>
                  <input
                    type="date"
                    value={newEvent.date}
                    onChange={(e) => setNewEvent({ ...newEvent, date: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {BISAYA_TAGALOG_TEXTS.time}
                  </label>
                  <input
                    type="time"
                    value={newEvent.time}
                    onChange={(e) => setNewEvent({ ...newEvent, time: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200"
                    required
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {BISAYA_TAGALOG_TEXTS.type} sa {BISAYA_TAGALOG_TEXTS.events}
                </label>
                <select
                  value={newEvent.type}
                  onChange={(e) => setNewEvent({ ...newEvent, type: e.target.value as Event['type'] })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200"
                >
                  <option value="reminder">{BISAYA_TAGALOG_TEXTS.reminder}</option>
                  <option value="delivery">{BISAYA_TAGALOG_TEXTS.delivery}</option>
                  <option value="meeting">{BISAYA_TAGALOG_TEXTS.meeting}</option>
                  <option value="holiday">{BISAYA_TAGALOG_TEXTS.holiday}</option>
                  <option value="personal">{BISAYA_TAGALOG_TEXTS.personal}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {BISAYA_TAGALOG_TEXTS.location} (Optional)
                </label>
                <input
                  type="text"
                  value={newEvent.location}
                  onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200"
                  placeholder="Asa ang event..."
                />
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsEventModalOpen(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 hover:scale-105"
                >
                  {BISAYA_TAGALOG_TEXTS.cancel}
                </button>
                <button
                  type="submit"
                  className="flex-1 btn-primary hover:scale-105 shadow-lg"
                >
                  {BISAYA_TAGALOG_TEXTS.addEvent}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
