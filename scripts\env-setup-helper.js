#!/usr/bin/env node

/**
 * 🔧 Environment Setup Helper for Revantad Store
 * 
 * This script helps you configure your .env.local file with proper credentials
 * and validates your setup for optimal development experience.
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan')
}

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

async function main() {
  log('\n' + '='.repeat(70), 'magenta')
  log('🚀 REVANTAD STORE - ENVIRONMENT SETUP HELPER', 'bright')
  log('='.repeat(70), 'magenta')
  
  logInfo('This helper will guide you through setting up your .env.local file')
  logInfo('Press Ctrl+C at any time to exit\n')

  const envPath = path.join(process.cwd(), '.env.local')
  
  // Check if .env.local exists
  if (!fs.existsSync(envPath)) {
    logError('.env.local file not found!')
    logInfo('Please ensure you have a .env.local file in your project root')
    process.exit(1)
  }

  // Read current .env.local
  const envContent = fs.readFileSync(envPath, 'utf8')
  
  log('\n📋 CURRENT CONFIGURATION STATUS:', 'cyan')
  log('─'.repeat(50), 'cyan')
  
  // Check each required variable
  const requiredVars = [
    {
      name: 'NEXT_PUBLIC_SUPABASE_URL',
      description: 'Supabase Project URL',
      placeholder: 'https://your-project-id.supabase.co',
      required: true
    },
    {
      name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      description: 'Supabase Anonymous Key',
      placeholder: 'your_supabase_anon_key_here',
      required: true
    },
    {
      name: 'SUPABASE_SERVICE_ROLE_KEY',
      description: 'Supabase Service Role Key',
      placeholder: 'your_supabase_service_role_key_here',
      required: false
    },
    {
      name: 'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
      description: 'Cloudinary Cloud Name',
      placeholder: 'your_cloudinary_cloud_name',
      required: true
    },
    {
      name: 'CLOUDINARY_API_KEY',
      description: 'Cloudinary API Key',
      placeholder: 'your_cloudinary_api_key',
      required: true
    },
    {
      name: 'CLOUDINARY_API_SECRET',
      description: 'Cloudinary API Secret',
      placeholder: 'your_cloudinary_api_secret',
      required: true
    },
    {
      name: 'NEXTAUTH_SECRET',
      description: 'NextAuth Secret Key',
      placeholder: 'your_nextauth_secret_key_here',
      required: true
    },
    {
      name: 'GEMINI_API_KEY',
      description: 'Google Gemini AI API Key',
      placeholder: 'your_gemini_api_key',
      required: false
    }
  ]

  let configuredCount = 0
  let missingRequired = []

  requiredVars.forEach(variable => {
    const regex = new RegExp(`^${variable.name}=(.+)`, 'm')
    const match = envContent.match(regex)
    
    if (match && match[1] && match[1] !== variable.placeholder) {
      logSuccess(`${variable.description}: Configured`)
      configuredCount++
    } else {
      if (variable.required) {
        logError(`${variable.description}: Missing or placeholder`)
        missingRequired.push(variable)
      } else {
        logWarning(`${variable.description}: Optional - Not configured`)
      }
    }
  })

  log(`\n📊 Configuration Status: ${configuredCount}/${requiredVars.filter(v => v.required).length} required variables configured`, 'cyan')

  if (missingRequired.length === 0) {
    logSuccess('\n🎉 All required environment variables are configured!')
    logInfo('You can now run: npm run dev')
  } else {
    logWarning(`\n⚠️  ${missingRequired.length} required variables need configuration`)
    
    const shouldSetup = await askQuestion('\nWould you like setup guidance? (y/n): ')
    
    if (shouldSetup.toLowerCase() === 'y' || shouldSetup.toLowerCase() === 'yes') {
      await provideSetupGuidance(missingRequired)
    }
  }

  rl.close()
}

async function provideSetupGuidance(missingVars) {
  log('\n🔧 SETUP GUIDANCE:', 'cyan')
  log('─'.repeat(50), 'cyan')

  for (const variable of missingVars) {
    log(`\n📝 Setting up: ${variable.description}`, 'yellow')
    
    switch (variable.name) {
      case 'NEXT_PUBLIC_SUPABASE_URL':
      case 'NEXT_PUBLIC_SUPABASE_ANON_KEY':
        logInfo('1. Go to https://supabase.com/dashboard')
        logInfo('2. Create a new project or select existing')
        logInfo('3. Go to Settings > API')
        logInfo('4. Copy the Project URL and anon/public key')
        logInfo('5. Run the SQL from database/schema.sql in SQL Editor')
        break
        
      case 'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME':
      case 'CLOUDINARY_API_KEY':
      case 'CLOUDINARY_API_SECRET':
        logInfo('1. Go to https://cloudinary.com/console')
        logInfo('2. Create account or sign in')
        logInfo('3. Copy Cloud Name, API Key, API Secret from Dashboard')
        logInfo('4. Create upload preset: "sari-sari-products" (unsigned)')
        break
        
      case 'NEXTAUTH_SECRET':
        logInfo('1. Generate a secure secret at: https://generate-secret.vercel.app/32')
        logInfo('2. Or run: openssl rand -base64 32')
        logInfo('3. Copy the generated secret')
        break
    }
    
    await askQuestion('Press Enter to continue...')
  }

  logInfo('\n📖 For detailed instructions, see: docs/SETUP_GUIDE.md')
  logInfo('After updating .env.local, run: npm run validate')
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  log('\n\n👋 Setup cancelled. Run this script again when ready!', 'yellow')
  rl.close()
  process.exit(0)
})

// Run the main function
main().catch(error => {
  logError(`Setup failed: ${error.message}`)
  rl.close()
  process.exit(1)
})
