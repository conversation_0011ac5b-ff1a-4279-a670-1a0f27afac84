import { NextRequest } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch customer balances with optional filtering (NO DEFAULT LIMIT)
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)

  // Optional filters
  const search = searchParams.get('search')
  const hasBalance = searchParams.get('has_balance') // 'true' to show only customers with remaining balance

  // Optional pagination - if not specified, return ALL balances
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : null

  // First get the basic balances
  let query = supabase
    .from('customer_balances')
    .select('*', { count: 'exact' })
    .order('remaining_balance', { ascending: false })

  // Apply pagination only if specified
  if (page && limit) {
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)
  }

  // Apply search filter
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%`)
  }

  // Filter customers with remaining balance
  if (hasBalance === 'true') {
    query = query.gt('remaining_balance', 0)
  }

  const { data: balances, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  // Return response with or without pagination
  const responseData: any = {
    balances: balances || [],
  }

  if (page && limit) {
    responseData.pagination = {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  } else {
    responseData.total = count || 0
  }

  return successResponse(responseData)
})
