// Environment variables validation and configuration

import { z } from 'zod'

// Define the schema for environment variables
const envSchema = z.object({
  // Node environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Supabase configuration
  NEXT_PUBLIC_SUPABASE_URL: z.string().optional(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
  
  // Cloudinary configuration
  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string().optional(),
  CLOUDINARY_API_KEY: z.string().optional(),
  CLOUDINARY_API_SECRET: z.string().optional(),

  // Google Gemini AI configuration
  GEMINI_API_KEY: z.string().optional(),
  
  // Authentication (optional for build time)
  NEXTAUTH_SECRET: z.string().optional(),
  NEXTAUTH_URL: z.string().optional(),
  
  // Optional configuration
  DEBUG: z.string().transform(val => val === 'true').default('false'),
})

// Parse and validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      throw new Error(
        `❌ Invalid environment variables:\n${missingVars.join('\n')}\n\n` +
        `Please check your .env.local file and ensure all required variables are set.\n` +
        `See .env.example for reference.`
      )
    }
    throw error
  }
}

// Export validated environment variables
export const env = validateEnv()

// Environment-specific configurations
export const config = {
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  isTest: env.NODE_ENV === 'test',
  
  // Database
  database: {
    url: env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key',
    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,
  },

  // File storage
  cloudinary: {
    cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'placeholder',
    apiKey: env.CLOUDINARY_API_KEY,
    apiSecret: env.CLOUDINARY_API_SECRET,
  },

  // AI configuration
  ai: {
    geminiApiKey: env.GEMINI_API_KEY,
  },
  
  // Authentication
  auth: {
    secret: env.NEXTAUTH_SECRET,
    url: env.NEXTAUTH_URL,
  },
  
  // Debug mode
  debug: env.DEBUG,
} as const

// Runtime environment checks
export function checkRequiredEnvVars() {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
  ]
  
  const missingVars = requiredVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    throw new Error(
      `❌ Missing required environment variables: ${missingVars.join(', ')}\n` +
      `Please check your .env.local file.`
    )
  }
}

// Development-only environment checks
export function checkDevelopmentEnvVars() {
  if (config.isDevelopment) {
    const devVars = [
      'CLOUDINARY_API_KEY',
      'CLOUDINARY_API_SECRET',
      'NEXTAUTH_SECRET',
    ]
    
    const missingDevVars = devVars.filter(varName => !process.env[varName])
    
    if (missingDevVars.length > 0) {
      console.warn(
        `⚠️  Missing development environment variables: ${missingDevVars.join(', ')}\n` +
        `Some features may not work properly.`
      )
    }
  }
}

// Production-only environment checks
export function checkProductionEnvVars() {
  if (config.isProduction) {
    const prodVars = [
      'SUPABASE_SERVICE_ROLE_KEY',
      'CLOUDINARY_API_KEY',
      'CLOUDINARY_API_SECRET',
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL',
    ]
    
    const missingProdVars = prodVars.filter(varName => !process.env[varName])
    
    if (missingProdVars.length > 0) {
      throw new Error(
        `❌ Missing production environment variables: ${missingProdVars.join(', ')}\n` +
        `These are required for production deployment.`
      )
    }
  }
}

// Initialize environment validation
export function initializeEnv() {
  try {
    checkRequiredEnvVars()
    checkDevelopmentEnvVars()
    checkProductionEnvVars()
    
    if (config.debug) {
      console.warn('✅ Environment variables validated successfully')
      console.warn('📊 Configuration:', {
        environment: env.NODE_ENV,
        database: !!config.database.url,
        cloudinary: !!config.cloudinary.cloudName,
        auth: !!config.auth.secret,
      })
    }
  } catch (error) {
    console.error(error)
    if (config.isProduction) {
      process.exit(1)
    }
  }
}

// Export individual environment variables for convenience
export const {
  NODE_ENV,
  NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY,
  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  CLOUDINARY_API_KEY,
  CLOUDINARY_API_SECRET,
  NEXTAUTH_SECRET,
  NEXTAUTH_URL,
  DEBUG,
  GEMINI_API_KEY,
} = env
