# 🏪 Revantad Store - Professional Admin Dashboard

> **Modern sari-sari store management system built for Filipino entrepreneurs**

A comprehensive, professional-grade admin dashboard for managing your sari-sari store operations. Features product inventory management, customer debt tracking (utang), business analytics, and more. Built with cutting-edge technologies for reliability and performance.

[![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.0-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![Supabase](https://img.shields.io/badge/Supabase-PostgreSQL-green?style=flat-square&logo=supabase)](https://supabase.com/)

## 🌟 Why Revantad Store?

- **🚀 Modern Technology Stack**: Built with Next.js 15, React 19, and TypeScript for maximum performance
- **📱 Mobile-First Design**: Responsive design that works perfectly on all devices
- **🔒 Secure & Reliable**: Enterprise-grade security with Supabase backend
- **📊 Business Intelligence**: Real-time analytics and reporting for better decision making
- **🎨 Beautiful UI/UX**: Professional design with dark/light mode support
- **⚡ Fast Performance**: Optimized for speed with Turbopack and modern web standards
- **🤖 AI-Powered Assistant**: Intelligent business support with Google Gemini AI integration

## 🚀 Quick Start

```bash
# 1. Clone the repository
git clone <your-repo-url>
cd tindahan

# 2. Install dependencies
npm install

# 3. Set up environment variables
cp .env.example .env.local
# Edit .env.local with your actual credentials

# 4. Set up database (run SQL from database/schema.sql in Supabase)
npm run db:setup

# 5. Start development server
npm run dev
```

Visit `http://localhost:3000` to see your dashboard!

## 📋 Table of Contents

- [Features](#-features)
- [Tech Stack](#-tech-stack)
- [Installation](#-installation)
- [Configuration](#-configuration)
- [Usage](#-usage)
- [API Documentation](#-api-documentation)
- [Deployment](#-deployment)
- [Contributing](#-contributing)
- [Support](#-support)

## ✨ Features

### 🏪 Professional Landing Page
- ✅ Beautiful hero section with Green and Mustard theme
- ✅ Feature showcase and customer testimonials
- ✅ Responsive design with modern animations
- ✅ Professional branding for Revantad Store

### 📱 Facebook-Style Admin Header
- ✅ Logo with return to front page functionality
- ✅ Advanced search bar for products, debts, and more
- ✅ Navigation icons for Dashboard, Products, Debts, Family Gallery
- ✅ Dark mode toggle and admin profile dropdown

### 📊 Product Lists (CRUD Operations)
- ✅ Create, Read, Update, Delete product entries
- ✅ Product image upload to Cloudinary
- ✅ Product details: name, net weight, price, stock quantity, category
- ✅ Low stock alerts and category filtering
- ✅ Advanced search functionality
- ✅ Responsive product grid layout

### 👥 Customer Management
- ✅ **Customer Profile Management**: Complete customer information with Cloudinary support
- ✅ **Customer Database**: Store customer details, contact information, and notes
- ✅ **Profile Pictures**: Upload and manage customer profile pictures via Cloudinary
- ✅ **Customer Search**: Find customers quickly with search functionality

### 📈 API Graphing & Visuals (ECharts)
- ✅ Monthly sales revenue line charts
- ✅ Product categories distribution pie charts
- ✅ Real-time KPI cards with business metrics
- ✅ Interactive data visualization
- ✅ Inventory analytics and trends

### 🖼️ Family Gallery
- ✅ Upload and manage family photos
- ✅ Like system and photo details
- ✅ Photo statistics and organization
- ✅ Full-screen photo viewing

### 📅 Calendar System
- ✅ Monthly calendar view with events
- ✅ Event management (delivery, meetings, reminders)
- ✅ Event categorization and scheduling
- ✅ Upcoming events overview

### 📋 History Tracking
- ✅ Complete activity logs for all actions
- ✅ Advanced filtering by type and date
- ✅ Export functionality for reports
- ✅ Activity statistics and insights

### ⚙️ Settings Management
- ✅ Store configuration and business hours
- ✅ Notification preferences
- ✅ Backup and data management
- ✅ Security and appearance settings

### 🤖 AI Assistant & Support
- ✅ **Intelligent Business Assistant**: Google Gemini AI-powered chat assistant
- ✅ **Contextual Help**: AI understands your current admin section for relevant advice
- ✅ **Business Analytics Support**: Get insights on sales, inventory, and customer behavior
- ✅ **Quick Action Prompts**: Pre-built prompts for common business questions
- ✅ **Floating Chat Interface**: Always accessible AI assistant with professional UI
- ✅ **Dedicated AI Support Page**: Full-featured AI consultation interface
- ✅ **Filipino Business Context**: AI trained on sari-sari store operations and culture
- ✅ **Real-time Responses**: Fast, intelligent responses for immediate business support

### 🌙 Dark Mode Support
- ✅ Complete dark theme implementation
- ✅ System theme detection
- ✅ Smooth theme transitions
- ✅ Persistent theme preferences

### 🎨 Modern UI/UX
- ✅ Green and Mustard color scheme
- ✅ Professional typography (Inter & Poppins)
- ✅ Smooth animations with Framer Motion
- ✅ Responsive design for all devices
- ✅ Loading states and error handling

## 🛠 Tech Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Frontend** | Next.js | 15.3.5 | React framework with SSR/SSG |
| **Frontend** | React | 19.0.0 | UI library |
| **Language** | TypeScript | 5.0+ | Type safety |
| **Styling** | Tailwind CSS | 4.0 | Utility-first CSS framework |
| **Database** | Supabase | Latest | PostgreSQL with real-time features |
| **Storage** | Cloudinary | Latest | Image storage and optimization |
| **Charts** | ECharts | 5.6.0 | Data visualization |
| **Animation** | Framer Motion | 12.23.0 | Smooth animations |
| **Icons** | Lucide React | 0.525.0 | Beautiful icons |
| **Forms** | React Hook Form | 7.60.0 | Form handling with validation |
| **Validation** | Zod | 3.25.75 | Schema validation |
| **Themes** | next-themes | 0.4.6 | Dark/light mode support |
| **AI** | Google Gemini AI | 0.24.1 | Intelligent business assistant |

## 📦 Installation

### Prerequisites

Ensure you have the following installed:
- **Node.js** 18.0.0 or higher
- **npm** 8.0.0 or higher
- **Git** for version control

### Required Services

You'll need accounts for:
- **[Supabase](https://supabase.com)** - Database and authentication
- **[Cloudinary](https://cloudinary.com)** - Image storage and optimization
- **[Google AI Studio](https://aistudio.google.com)** - Gemini AI API for intelligent assistant

### Step-by-Step Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd tindahan
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   Edit `.env.local` with your actual credentials (see [Configuration](#-configuration))

4. **Set up database**
   - Go to your Supabase dashboard
   - Navigate to SQL Editor
   - Copy the **entire contents** of `database/schema.sql`
   - Paste and run in SQL Editor
   - ✅ Creates all tables, functions, indexes, and sample data

5. **Start development server**
   ```bash
   npm run dev
   ```

Your dashboard will be available at `http://localhost:3000`

## ⚙️ Configuration

### Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Authentication
NEXTAUTH_SECRET=your_secure_random_string
NEXTAUTH_URL=http://localhost:3000

# AI Configuration
GEMINI_API_KEY=your_gemini_api_key
```

### 🗄️ **Database Setup**

#### ⚡ Unified Schema Setup (RECOMMENDED)
1. Create a new Supabase project
2. Go to **SQL Editor** in your Supabase dashboard
3. Open `database/tindahan_unified_schema.sql` and **copy all contents**
4. **Paste into SQL Editor** and click **"Run"**
5. ✅ **Done!** Complete system is created automatically:
   - 4 Tables (products, customers, customer_debts, customer_payments)
   - 1 Enhanced View (customer_balances with status indicators)
   - 2 Business Logic Functions (timestamp + validation)
   - 20+ Performance Indexes (including fuzzy search)
   - 4 Automatic Triggers
   - Row Level Security policies
   - 25+ sample products, 8 customers, 15+ debts, 12+ payments

#### 📋 What You Get with Unified Schema
- **Complete Store Management**: All features in one deployment
- **Production-Ready**: Enhanced security and performance
- **Comprehensive Sample Data**: Realistic test data across all modules
- **Advanced Features**: Fuzzy search, payment validation, status tracking
- **Single File Deployment**: No need for multiple schema files

**🎯 Important:** The unified schema (`tindahan_unified_schema.sql`) is the master file that replaces all other database files. Use this for new deployments.

### Cloudinary Setup

1. Create a Cloudinary account
2. Go to your dashboard to get your credentials
3. Create an upload preset named `sari-sari-products` (unsigned)

### Google AI Setup

1. Go to [Google AI Studio](https://aistudio.google.com)
2. Create a new project or select an existing one
3. Generate an API key for Gemini AI
4. Add the API key to your `.env.local` file as `GEMINI_API_KEY`

## 📖 Usage

### Default Login Credentials

For development and testing:
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Main Features

1. **Dashboard**: Overview of your store's performance
2. **Products**: Manage your inventory with CRUD operations
3. **Debts**: Track customer debt (utang) records
4. **Analytics**: View business insights and charts
5. **Family Gallery**: Manage family photos
6. **Calendar**: Track important dates and events
7. **Settings**: Configure your store preferences
8. **AI Support**: Get intelligent business assistance and insights

## 🔧 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with Turbopack |
| `npm run build` | Build for production |
| `npm run start` | Start production server |
| `npm run lint` | Run ESLint |
| `npm run lint:fix` | Fix ESLint issues automatically |
| `npm run type-check` | Run TypeScript type checking |
| `npm run clean` | Clean build cache |
| `npm run preview` | Build and start production preview |

## 🚀 Deployment

For detailed deployment instructions, see [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md).

### Quick Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/revantad-store)

## 📁 Project Structure

```
tindahan/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── admin/          # Admin dashboard pages
│   │   ├── api/            # API routes
│   │   ├── landing/        # Landing page
│   │   ├── login/          # Authentication
│   │   └── globals.css     # Global styles
│   ├── components/         # React components
│   ├── contexts/           # React contexts
│   └── lib/               # Utility libraries
├── database/              # Database schema
├── docs/                  # Documentation
├── public/               # Static assets
└── package.json          # Dependencies and scripts
```

## 📖 Usage Guide

### Dashboard Overview
- View key statistics: total products, customer debts, debt amounts, and low stock items
- Quick access to main features

### Managing Product Lists
1. Click "Product Lists" in the sidebar
2. Use "Add to Product List" to create new product entries
3. Upload product images via the image upload field
4. Edit or delete products using the action buttons
5. Use search and category filters to find specific products

### Managing Customer Debts
1. Click "Customer Debts" in the sidebar
2. Use "Add Debt Record" to create new debt entries
3. Records are grouped by customer showing total amounts owed
4. Edit or delete individual debt records
5. Use search to find specific customers or products

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Create new product
- `GET /api/products/[id]` - Get single product
- `PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Delete product

### Customer Debts
- `GET /api/debts` - Get all debt records
- `POST /api/debts` - Create new debt record
- `GET /api/debts/[id]` - Get single debt record
- `PUT /api/debts/[id]` - Update debt record
- `DELETE /api/debts/[id]` - Delete debt record

### File Upload
- `POST /api/upload` - Upload image to Cloudinary

## Project Structure

```
sari-sari-admin/
├── src/
│   ├── app/
│   │   ├── api/          # API routes
│   │   ├── globals.css   # Global styles
│   │   ├── layout.tsx    # Root layout
│   │   └── page.tsx      # Main dashboard page
│   └── components/       # React components
│       ├── Sidebar.tsx
│       ├── DashboardStats.tsx
│       ├── ProductsSection.tsx
│       ├── ProductModal.tsx
│       ├── DebtsSection.tsx
│       └── DebtModal.tsx
├── lib/
│   ├── supabase.ts      # Supabase configuration
│   └── cloudinary.ts    # Cloudinary configuration
├── database/
│   └── schema.sql       # Database schema
└── public/              # Static assets
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please create an issue in the repository.
