import { NextRequest, NextResponse } from 'next/server'

import cloudinary from '@/lib/cloudinary'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const oldPublicId = formData.get('old_public_id') as string | null

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create unique public_id for Cloudinary
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const publicId = `sari-sari-products/product_${timestamp}_${randomString}`

    // Upload to Cloudinary
    const result = await new Promise<{ secure_url: string; public_id: string }>((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: 'image',
          public_id: publicId,
          folder: 'sari-sari-products',
          transformation: [
            { width: 500, height: 500, crop: 'limit' },
            { quality: 'auto:best' },
            { format: 'auto' },
            { flags: 'progressive' }
          ]
        },
        (error, result) => {
          if (error) {
            console.error('Cloudinary upload error:', error)
            reject(new Error(`Cloudinary upload failed: ${error.message}`))
          } else if (result) {
            resolve({ secure_url: result.secure_url, public_id: result.public_id })
          } else {
            reject(new Error('Upload failed: No result returned from Cloudinary'))
          }
        }
      ).end(buffer)
    })

    // Delete old image from Cloudinary if it exists
    if (oldPublicId) {
      try {
        console.log(`Deleting old product image: ${oldPublicId}`)
        const deleteResult = await cloudinary.uploader.destroy(oldPublicId)
        console.log(`Delete result for ${oldPublicId}:`, deleteResult)

        if (deleteResult.result === 'ok') {
          console.log(`Successfully deleted old product image: ${oldPublicId}`)
        } else if (deleteResult.result === 'not found') {
          console.log(`Old product image not found in Cloudinary: ${oldPublicId}`)
        } else {
          console.warn(`Failed to delete old product image: ${oldPublicId}`, deleteResult)
        }
      } catch (deleteError) {
        // Don't fail the upload if deletion fails - just log the error
        console.error(`Error deleting old product image ${oldPublicId}:`, deleteError)
      }
    }

    // Add cache-busting parameter to ensure fresh image loads
    const cacheBustUrl = `${result.secure_url}?cache_bust=${Date.now()}`

    return NextResponse.json({
      success: true,
      url: cacheBustUrl,
      public_id: result.public_id
    })
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Failed to upload image' },
      { status: 500 }
    )
  }
}

// DELETE - Remove product image from Cloudinary
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const publicId = searchParams.get('public_id')

    if (!publicId) {
      return NextResponse.json({ error: 'Public ID is required' }, { status: 400 })
    }

    // Delete from Cloudinary
    const result = await cloudinary.uploader.destroy(publicId)

    if (result.result === 'ok') {
      return NextResponse.json({
        success: true,
        message: 'Product image deleted successfully from cloud storage'
      })
    } else {
      return NextResponse.json({
        error: 'Failed to delete image from cloud storage'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Error deleting product image from Cloudinary:', error)
    return NextResponse.json(
      { error: 'Failed to delete image from cloud storage' },
      { status: 500 }
    )
  }
}
