import { NextRequest } from 'next/server'

import {
  successResponse,
  errorResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequestBody,
  validate<PERSON>equired<PERSON><PERSON>s,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'


// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all products with optional filtering (NO DEFAULT LIMIT)
export const GET = withErrorHandler(async (request: NextRequest) => {
  console.log('🔄 Products API called')

  const { searchParams } = new URL(request.url)

  // Optional filters
  const category = searchParams.get('category')
  const search = searchParams.get('search')
  const lowStock = searchParams.get('lowStock') === 'true'

  // Optional pagination - if not specified, return ALL products
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : null

  console.log('🔍 Filters:', { category, search, lowStock, page, limit })

  let query = supabase
    .from('products')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })

  // Apply pagination only if specified
  if (page && limit) {
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)
    console.log('📊 Using pagination:', { page, limit, offset })
  } else {
    console.log('📊 No pagination - returning ALL products')
  }

  // Apply filters
  if (category) {
    query = query.eq('category', category)
  }

  if (search) {
    query = query.ilike('name', `%${search}%`)
  }

  if (lowStock) {
    query = query.lt('stock_quantity', 10)
  }

  console.log('🔄 Executing database query...')
  const { data: products, error, count } = await query

  if (error) {
    console.error('❌ Database error:', error)
    handleDatabaseError(error)
  }

  console.log('✅ Query successful:', {
    productsCount: products?.length || 0,
    totalCount: count
  })

  // Return response with or without pagination
  const responseData: any = {
    products: products || [],
  }

  if (page && limit) {
    responseData.pagination = {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  } else {
    responseData.total = count || 0
  }

  return successResponse(responseData)
})

// POST - Create new product
export const POST = withErrorHandler(async (request: NextRequest) => {
  const productData = await validateRequestBody(request, (body) => {
    // Type assertion for body to ensure it's an object
    const bodyData = body as Record<string, unknown>

    // Validate required fields
    validateRequiredFields(bodyData, ['name', 'net_weight', 'price', 'category'])

    return {
      name: String(bodyData.name).trim(),
      image_url: bodyData.image_url ? String(bodyData.image_url).trim() : null,
      image_public_id: bodyData.image_public_id ? String(bodyData.image_public_id).trim() : null,
      net_weight: String(bodyData.net_weight).trim(),
      price: parseFloat(bodyData.price as string),
      stock_quantity: parseInt(bodyData.stock_quantity as string) || 0,
      category: String(bodyData.category).trim(),
    }
  })

  // Additional validation
  if (productData.price < 0) {
    return errorResponse('Price must be a positive number', 400)
  }

  if (productData.stock_quantity < 0) {
    return errorResponse('Stock quantity must be a positive number', 400)
  }

  const { data: product, error } = await supabase
    .from('products')
    .insert([productData])
    .select()
    .single()

  if (error) {
    handleDatabaseError(error)
  }

  return successResponse(product, 'Product created successfully', 201)
})
