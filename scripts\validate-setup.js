#!/usr/bin/env node

/**
 * Setup Validation Script for Revantad Store Admin Dashboard
 * This script validates that all setup requirements are met
 */

const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

function validateFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    logSuccess(`${description} exists`)
    return true
  } else {
    logError(`${description} missing: ${filePath}`)
    return false
  }
}

function validateEnvironmentFile() {
  const envLocalPath = path.join(process.cwd(), '.env.local')
  const envExamplePath = path.join(process.cwd(), '.env.example')
  
  if (!validateFileExists(envLocalPath, '.env.local file')) {
    if (fs.existsSync(envExamplePath)) {
      logWarning('Copy .env.example to .env.local and configure your credentials')
    }
    return false
  }
  
  const envContent = fs.readFileSync(envLocalPath, 'utf8')
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
    'NEXTAUTH_SECRET',
  ]
  
  const configuredVars = []
  const missingVars = []
  
  requiredVars.forEach(varName => {
    const regex = new RegExp(`^${varName}=(.+)`, 'm')
    const match = envContent.match(regex)
    if (match && match[1] && !match[1].includes('your_') && !match[1].includes('placeholder')) {
      configuredVars.push(varName)
    } else {
      missingVars.push(varName)
    }
  })
  
  configuredVars.forEach(varName => {
    logSuccess(`${varName} configured`)
  })
  
  missingVars.forEach(varName => {
    logWarning(`${varName} needs configuration`)
  })
  
  return missingVars.length === 0
}

function validateDatabaseSchema() {
  const schemaPath = path.join(process.cwd(), 'database', 'schema.sql')
  if (!validateFileExists(schemaPath, 'Database schema file')) {
    return false
  }
  
  const schemaContent = fs.readFileSync(schemaPath, 'utf8')
  const requiredTables = ['products', 'customer_debts']
  const requiredElements = [
    'CREATE TABLE',
    'CREATE INDEX',
    'CREATE TRIGGER',
    'INSERT INTO products',
    'INSERT INTO customer_debts'
  ]
  
  let allElementsFound = true
  
  requiredElements.forEach(element => {
    if (schemaContent.includes(element)) {
      logSuccess(`Schema contains ${element}`)
    } else {
      logError(`Schema missing ${element}`)
      allElementsFound = false
    }
  })
  
  return allElementsFound
}

function validateProjectStructure() {
  const requiredDirs = [
    'src/app',
    'src/components',
    'src/lib',
    'src/types',
    'database',
    'docs',
    'scripts'
  ]
  
  const requiredFiles = [
    'package.json',
    'tsconfig.json',
    'tailwind.config.js',
    'next.config.ts',
    '.env.example'
  ]
  
  let structureValid = true
  
  requiredDirs.forEach(dir => {
    if (!validateFileExists(dir, `Directory ${dir}`)) {
      structureValid = false
    }
  })
  
  requiredFiles.forEach(file => {
    if (!validateFileExists(file, `File ${file}`)) {
      structureValid = false
    }
  })
  
  return structureValid
}

function validatePackageJson() {
  const packagePath = path.join(process.cwd(), 'package.json')
  if (!fs.existsSync(packagePath)) {
    logError('package.json not found')
    return false
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  const requiredScripts = ['dev', 'build', 'start', 'lint', 'setup']
  const requiredDeps = ['next', 'react', 'typescript', '@supabase/supabase-js', 'cloudinary']
  
  let scriptsValid = true
  let depsValid = true
  
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      logSuccess(`Script '${script}' configured`)
    } else {
      logError(`Script '${script}' missing`)
      scriptsValid = false
    }
  })
  
  requiredDeps.forEach(dep => {
    const inDeps = packageJson.dependencies && packageJson.dependencies[dep]
    const inDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep]

    if (inDeps || inDevDeps) {
      logSuccess(`Dependency '${dep}' installed`)
    } else {
      logError(`Dependency '${dep}' missing`)
      depsValid = false
    }
  })
  
  return scriptsValid && depsValid
}

function displaySetupStatus() {
  log('\n' + '='.repeat(60), 'cyan')
  log('🏪 Revantad Store Setup Validation', 'bright')
  log('='.repeat(60), 'cyan')
  
  const checks = [
    { name: 'Project Structure', fn: validateProjectStructure },
    { name: 'Package Configuration', fn: validatePackageJson },
    { name: 'Environment Variables', fn: validateEnvironmentFile },
    { name: 'Database Schema', fn: validateDatabaseSchema },
  ]
  
  let allPassed = true
  
  checks.forEach(check => {
    log(`\n📋 Checking ${check.name}...`, 'cyan')
    const passed = check.fn()
    if (!passed) {
      allPassed = false
    }
  })
  
  log('\n' + '='.repeat(60), 'cyan')
  if (allPassed) {
    log('🎉 All setup requirements validated successfully!', 'green')
    log('Your Revantad Store Admin Dashboard is ready to run!', 'green')
    log('\nNext steps:', 'cyan')
    log('1. npm run dev - Start the development server')
    log('2. Open http://localhost:3000 in your browser')
    log('3. Login with: <EMAIL> / admin123')
  } else {
    log('⚠️  Some setup requirements need attention', 'yellow')
    log('Please review the issues above and refer to docs/SETUP_GUIDE.md', 'yellow')
  }
  log('='.repeat(60), 'cyan')
}

// Run validation
if (require.main === module) {
  displaySetupStatus()
}

module.exports = {
  validateFileExists,
  validateEnvironmentFile,
  validateDatabaseSchema,
  validateProjectStructure,
  validatePackageJson,
}
