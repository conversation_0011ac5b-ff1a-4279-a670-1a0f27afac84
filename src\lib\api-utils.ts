// API utilities for consistent error handling and response formatting

import { NextResponse } from 'next/server'

import type { ApiResponse } from '@/types'

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const

// Error types
export class ApiError extends Error {
  constructor(
    public message: string,
    public statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// Success response helper
export function successResponse<T>(
  data: T,
  message?: string,
  statusCode: number = HTTP_STATUS.OK
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
  }

  if (message) {
    response.message = message
  }

  return NextResponse.json(response, { status: statusCode })
}

// Error response helper
export function errorResponse(
  message: string,
  statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
  code?: string
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error: message,
      code,
    },
    { status: statusCode }
  )
}

// Validation error response
export function validationErrorResponse(
  errors: Record<string, string[]>
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error: 'Validation failed',
      data: errors,
    },
    { status: HTTP_STATUS.UNPROCESSABLE_ENTITY }
  )
}

// Method not allowed response
export function methodNotAllowedResponse(
  allowedMethods: string[]
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error: `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
    },
    { 
      status: HTTP_STATUS.METHOD_NOT_ALLOWED,
      headers: {
        Allow: allowedMethods.join(', ')
      }
    }
  )
}

// Async error handler wrapper
export function withErrorHandler<T extends unknown[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse<ApiResponse>> => {
    try {
      return await handler(...args)
    } catch (error) {
      console.error('API Error:', error)
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.statusCode, error.code)
      }
      
      if (error instanceof Error) {
        return errorResponse(error.message)
      }
      
      return errorResponse('An unexpected error occurred')
    }
  }
}

// Request validation helpers
export async function validateRequestBody<T>(
  request: Request,
  validator: (data: unknown) => T
): Promise<T> {
  try {
    const body = await request.json()
    return validator(body)
  } catch {
    throw new ApiError('Invalid request body', HTTP_STATUS.BAD_REQUEST)
  }
}

export function validateRequiredFields<T extends Record<string, unknown>>(
  data: T,
  requiredFields: (keyof T)[]
): void {
  const missingFields = requiredFields.filter(field => 
    data[field] === undefined || data[field] === null || data[field] === ''
  )
  
  if (missingFields.length > 0) {
    throw new ApiError(
      `Missing required fields: ${missingFields.join(', ')}`,
      HTTP_STATUS.BAD_REQUEST
    )
  }
}

// Database error handler
export function handleDatabaseError(error: Error & { code?: string }): never {
  console.error('Database error:', error)
  
  // Handle specific database errors
  if (error.code === '23505') { // Unique constraint violation
    throw new ApiError('Resource already exists', HTTP_STATUS.CONFLICT)
  }
  
  if (error.code === '23503') { // Foreign key constraint violation
    throw new ApiError('Referenced resource not found', HTTP_STATUS.BAD_REQUEST)
  }
  
  if (error.code === '23502') { // Not null constraint violation
    throw new ApiError('Required field is missing', HTTP_STATUS.BAD_REQUEST)
  }
  
  // Generic database error
  throw new ApiError('Database operation failed', HTTP_STATUS.INTERNAL_SERVER_ERROR)
}

// Pagination helpers
export interface PaginationOptions {
  page?: number
  limit?: number
  maxLimit?: number
}

export function parsePaginationParams(
  searchParams: URLSearchParams,
  options: PaginationOptions = {}
): { page: number; limit: number; offset: number } {
  const { maxLimit = 10000 } = options

  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10))
  const limit = Math.min(
    maxLimit,
    Math.max(1, parseInt(searchParams.get('limit') || '1000', 10)) // Default to 1000 instead of 10
  )
  const offset = (page - 1) * limit

  return { page, limit, offset }
}

// CORS headers for API routes
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

// Handle CORS preflight requests
export function handleCorsPreflightRequest(): NextResponse {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  })
}
