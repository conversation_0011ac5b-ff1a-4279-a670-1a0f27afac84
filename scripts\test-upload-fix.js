#!/usr/bin/env node

/**
 * Test Script for Upload Functionality Fix
 * 
 * This script tests the file upload functionality to ensure that
 * the ProfilePictureUpload component can successfully upload images
 * to Cloudinary via the API route.
 */

import dotenv from 'dotenv'
import fetch from 'node-fetch'
import fs from 'fs'
import path from 'path'

// Load environment variables
dotenv.config({ path: '.env.local' })

const API_BASE_URL = 'http://localhost:3001'

async function testUploadConfiguration() {
  console.log('🧪 Testing Upload Configuration')
  console.log('================================')

  // Check environment variables
  console.log('🔧 Checking environment variables...')
  
  const requiredVars = [
    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
    'CLOUDINARY_API_KEY', 
    'CLOUDINARY_API_SECRET'
  ]

  const missingVars = requiredVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    console.error('❌ Missing environment variables:', missingVars.join(', '))
    console.error('Please check your .env.local file')
    return false
  }

  console.log('✅ All required environment variables are set')
  console.log('   - Cloud Name:', process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME)
  console.log('   - API Key:', process.env.CLOUDINARY_API_KEY ? '***' + process.env.CLOUDINARY_API_KEY.slice(-4) : 'Not set')
  console.log('   - API Secret:', process.env.CLOUDINARY_API_SECRET ? '***' + process.env.CLOUDINARY_API_SECRET.slice(-4) : 'Not set')

  return true
}

async function testAPIEndpoint() {
  console.log('\n🌐 Testing API endpoint...')
  
  try {
    // Test if the server is running
    const response = await fetch(`${API_BASE_URL}/api/upload/profile-picture`, {
      method: 'POST',
      body: new FormData() // Empty form data to test endpoint
    })

    if (response.status === 400) {
      const data = await response.json()
      if (data.error === 'No file uploaded') {
        console.log('✅ API endpoint is accessible and responding correctly')
        return true
      }
    }

    console.log('⚠️  API endpoint response:', response.status, await response.text())
    return false
  } catch (error) {
    console.error('❌ Failed to connect to API endpoint:', error.message)
    console.error('   Make sure your development server is running on port 3001')
    return false
  }
}

async function createTestImage() {
  console.log('\n🖼️  Creating test image...')
  
  // Create a simple test image (1x1 pixel PNG)
  const testImageData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
    0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, // bit depth, color type, etc.
    0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
    0x54, 0x08, 0xD7, 0x63, 0xF8, 0x0F, 0x00, 0x00, // image data
    0x01, 0x00, 0x01, 0x5C, 0xC2, 0xD2, 0x3D, 0x00, // checksum
    0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, // IEND chunk
    0x42, 0x60, 0x82
  ])

  const testImagePath = path.join(process.cwd(), 'test-image.png')
  fs.writeFileSync(testImagePath, testImageData)
  
  console.log('✅ Test image created:', testImagePath)
  return testImagePath
}

async function testImageUpload() {
  console.log('\n📤 Testing image upload...')
  
  try {
    const testImagePath = await createTestImage()
    
    // Create form data with test image
    const formData = new FormData()
    const imageBuffer = fs.readFileSync(testImagePath)
    const blob = new Blob([imageBuffer], { type: 'image/png' })
    formData.append('file', blob, 'test-image.png')

    // Upload the image
    const response = await fetch(`${API_BASE_URL}/api/upload/profile-picture`, {
      method: 'POST',
      body: formData
    })

    const responseData = await response.json()

    if (response.ok && responseData.success) {
      console.log('✅ Image upload successful!')
      console.log('   - URL:', responseData.url)
      console.log('   - Public ID:', responseData.public_id)
      
      // Clean up test image
      fs.unlinkSync(testImagePath)
      console.log('✅ Test image cleaned up')
      
      return true
    } else {
      console.error('❌ Image upload failed:', responseData.error || 'Unknown error')
      console.error('   Response status:', response.status)
      console.error('   Response data:', responseData)
      
      // Clean up test image
      if (fs.existsSync(testImagePath)) {
        fs.unlinkSync(testImagePath)
      }
      
      return false
    }
  } catch (error) {
    console.error('❌ Upload test failed:', error.message)
    return false
  }
}

async function runUploadTests() {
  console.log('🚀 Starting Upload Functionality Tests')
  console.log('======================================')

  try {
    // Test 1: Environment configuration
    const configOk = await testUploadConfiguration()
    if (!configOk) {
      console.log('\n❌ Configuration test failed. Please fix environment variables.')
      process.exit(1)
    }

    // Test 2: API endpoint accessibility
    const apiOk = await testAPIEndpoint()
    if (!apiOk) {
      console.log('\n❌ API endpoint test failed. Please check your development server.')
      process.exit(1)
    }

    // Test 3: Actual image upload
    const uploadOk = await testImageUpload()
    if (!uploadOk) {
      console.log('\n❌ Image upload test failed. Please check Cloudinary configuration.')
      process.exit(1)
    }

    console.log('\n🎉 SUCCESS: All upload tests passed!')
    console.log('✅ Environment variables configured correctly')
    console.log('✅ API endpoint accessible and working')
    console.log('✅ Image upload to Cloudinary successful')
    console.log('\nThe upload functionality should now work properly in your application.')

  } catch (error) {
    console.error('❌ Test suite failed:', error.message)
    process.exit(1)
  }
}

// Run the tests
runUploadTests()
