-- =====================================================
-- OVERPAYMENT AND CHANGE CALCULATION FIX
-- =====================================================
-- This script fixes the issue where overpayments show negative remaining balance
-- Instead, it should show zero remaining balance and calculate change/sukli

-- Drop the existing view first
DROP VIEW IF EXISTS customer_balances;

-- Create enhanced customer_balances view with proper overpayment handling
CREATE VIEW customer_balances
WITH (security_invoker = true) AS
SELECT
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    
    -- FIXED: Remaining balance should never be negative
    CASE 
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) > 0 
        THEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0)
        ELSE 0
    END as remaining_balance,
    
    -- NEW: Change/Sukli calculation for overpayments
    CASE 
        WHEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0) > 0 
        THEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0)
        ELSE 0
    END as change_amount,
    
    last_debt_date,
    last_payment_date,
    debt_count,
    payment_count,
    
    -- Enhanced status indicators with overpayment status
    CASE
        WHEN COALESCE(total_debt, 0) = 0 THEN 'No Debt'
        WHEN COALESCE(total_payments, 0) - COALESCE(total_debt, 0) > 0 THEN 'Overpaid'
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) = 0 THEN 'Paid'
        WHEN COALESCE(total_debt, 0) - COALESCE(total_payments, 0) > 0 AND
             COALESCE(total_payments, 0) > 0 THEN 'Outstanding'
        WHEN COALESCE(total_debt, 0) > 0 AND COALESCE(total_payments, 0) = 0 THEN 'Unpaid'
        ELSE 'Unknown'
    END as balance_status,
    
    -- Payment percentage with better precision (capped at 100%)
    ROUND(
        CASE
            WHEN COALESCE(total_debt, 0) > 0
            THEN LEAST((COALESCE(total_payments, 0) / total_debt) * 100, 100)
            ELSE 0
        END, 2
    ) as payment_percentage,
    
    -- Additional useful metrics
    CASE
        WHEN last_debt_date IS NOT NULL THEN
            (CURRENT_DATE - last_debt_date)
        ELSE NULL
    END as days_since_last_debt,
    CASE
        WHEN last_payment_date IS NOT NULL THEN
            (CURRENT_DATE - last_payment_date)
        ELSE NULL
    END as days_since_last_payment
FROM (
    SELECT
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date,
        COUNT(*) as debt_count
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date,
        COUNT(*) as payment_count
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- Test the fix with sample data
SELECT 
    'OVERPAYMENT FIX TEST:' as info,
    customer_name || ' ' || customer_family_name as customer,
    'PHP ' || total_debt::text as total_debt,
    'PHP ' || total_payments::text as total_payments,
    'PHP ' || remaining_balance::text as remaining_balance,
    'PHP ' || change_amount::text as change_sukli,
    balance_status
FROM customer_balances
WHERE total_payments > total_debt
ORDER BY change_amount DESC;

-- Verify all customers
SELECT 
    'ALL CUSTOMERS AFTER FIX:' as info,
    customer_name || ' ' || customer_family_name as customer,
    'PHP ' || total_debt::text as total_debt,
    'PHP ' || total_payments::text as total_payments,
    'PHP ' || remaining_balance::text as remaining_balance,
    'PHP ' || change_amount::text as change_sukli,
    balance_status
FROM customer_balances
ORDER BY remaining_balance DESC, change_amount DESC;
